// Add this line at the very top
import dotenv from 'dotenv';
dotenv.config(); // <--- THIS IS THE CRITICAL LINE TO ADD HERE

import { createServer } from 'http';
import app from './src/app'; // Import the Express app from src/app.ts
import { connectDB } from './src/config/db'; // Import the DB connection function
import { PORT } from './src/config/env'; // Import the port from your env config (now it will correctly read from process.env)
import { initializeSocket } from './src/socket/socketHandler'; // Import Socket.IO handler

// Connect to MongoDB
connectDB();

// Create HTTP server
const server = createServer(app);

// Initialize Socket.IO
const io = initializeSocket(server);

// Start the server with Socket.IO support
server.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
    console.log('Socket.IO initialized and ready for connections');
});