// jobFinderApp/src/screens/employer/AnalyticsTestScreen.tsx
// Simple test screen to verify analytics functionality

import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import Ionicons from 'react-native-vector-icons/Ionicons';

const AnalyticsTestScreen: React.FC = () => {
    const { theme } = useTheme();

    const mockData = {
        overview: {
            total_jobs: 12,
            active_jobs: 8,
            total_applications: 156,
            total_views: 2340,
            avg_conversion_rate: 6.7,
        },
        top_jobs: [
            { id: '1', title: 'Senior React Developer', applications: 45, views: 320 },
            { id: '2', title: 'Product Manager', applications: 38, views: 280 }
        ],
        sources: [
            { source: 'LinkedIn', count: 85, percentage: 54 },
            { source: 'Indeed', count: 42, percentage: 27 }
        ]
    };

    const renderMetricCard = (title: string, value: string | number, icon: string, color: string) => (
        <View style={[styles.metricCard, { backgroundColor: theme.surface }]}>
            <View style={styles.metricHeader}>
                <View style={[styles.metricIcon, { backgroundColor: color + '15' }]}>
                    <Ionicons name={icon as any} size={24} color={color} />
                </View>
                <View style={styles.metricContent}>
                    <Text style={[styles.metricValue, { color: theme.text }]}>{value}</Text>
                    <Text style={[styles.metricTitle, { color: theme.textSecondary }]}>{title}</Text>
                </View>
            </View>
        </View>
    );

    return (
        <View style={[styles.container, { backgroundColor: theme.background }]}>
            {/* Simple Header */}
            <View style={[styles.header, { backgroundColor: theme.primary }]}>
                <Text style={styles.headerTitle}>Analytics Test</Text>
                <Text style={styles.headerSubtitle}>Verifying component functionality</Text>
            </View>

            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                {/* Overview Metrics */}
                <View style={styles.section}>
                    <Text style={[styles.sectionTitle, { color: theme.text }]}>Overview</Text>
                    <View style={styles.metricsRow}>
                        {renderMetricCard('Total Jobs', mockData.overview.total_jobs, 'briefcase-outline', theme.primary)}
                        {renderMetricCard('Applications', mockData.overview.total_applications, 'people-outline', theme.success)}
                    </View>
                    <View style={styles.metricsRow}>
                        {renderMetricCard('Views', mockData.overview.total_views.toLocaleString(), 'eye-outline', theme.accent)}
                        {renderMetricCard('Conversion', `${mockData.overview.avg_conversion_rate}%`, 'trending-up-outline', theme.warning)}
                    </View>
                </View>

                {/* Top Jobs */}
                <View style={styles.section}>
                    <Text style={[styles.sectionTitle, { color: theme.text }]}>Top Performing Jobs</Text>
                    {mockData.top_jobs.map((job, index) => (
                        <View key={job.id} style={[styles.jobCard, { backgroundColor: theme.surface }]}>
                            <View style={[styles.jobRank, { backgroundColor: theme.primary }]}>
                                <Text style={styles.rankText}>#{index + 1}</Text>
                            </View>
                            <View style={styles.jobInfo}>
                                <Text style={[styles.jobTitle, { color: theme.text }]}>{job.title}</Text>
                                <Text style={[styles.jobStats, { color: theme.textSecondary }]}>
                                    {job.applications} applications • {job.views} views
                                </Text>
                            </View>
                        </View>
                    ))}
                </View>

                {/* Application Sources */}
                <View style={styles.section}>
                    <Text style={[styles.sectionTitle, { color: theme.text }]}>Application Sources</Text>
                    {mockData.sources.map((source, index) => (
                        <View key={source.source} style={[styles.sourceCard, { backgroundColor: theme.surface }]}>
                            <View style={styles.sourceHeader}>
                                <Text style={[styles.sourceName, { color: theme.text }]}>{source.source}</Text>
                                <Text style={[styles.sourcePercentage, { color: theme.primary }]}>{source.percentage}%</Text>
                            </View>
                            <View style={[styles.progressBar, { backgroundColor: theme.border }]}>
                                <View 
                                    style={[
                                        styles.progressFill, 
                                        { backgroundColor: theme.primary, width: `${source.percentage}%` }
                                    ]} 
                                />
                            </View>
                        </View>
                    ))}
                </View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        paddingTop: 60,
        paddingBottom: 20,
        paddingHorizontal: 20,
        height: 140,
    },
    headerTitle: {
        fontSize: 28,
        fontWeight: 'bold',
        color: 'white',
        marginBottom: 4,
    },
    headerSubtitle: {
        fontSize: 16,
        color: 'rgba(255, 255, 255, 0.8)',
    },
    content: {
        flex: 1,
    },
    section: {
        paddingHorizontal: 20,
        marginBottom: 24,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    metricsRow: {
        flexDirection: 'row',
        marginBottom: 12,
        gap: 12,
    },
    metricCard: {
        flex: 1,
        padding: 16,
        borderRadius: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    metricHeader: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    metricIcon: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    metricContent: {
        flex: 1,
    },
    metricValue: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 2,
    },
    metricTitle: {
        fontSize: 14,
        fontWeight: '500',
    },
    jobCard: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderRadius: 12,
        marginBottom: 8,
        elevation: 1,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
    },
    jobRank: {
        width: 32,
        height: 32,
        borderRadius: 16,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    rankText: {
        fontSize: 14,
        fontWeight: 'bold',
        color: 'white',
    },
    jobInfo: {
        flex: 1,
    },
    jobTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 4,
    },
    jobStats: {
        fontSize: 12,
    },
    sourceCard: {
        padding: 16,
        borderRadius: 12,
        marginBottom: 8,
        elevation: 1,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
    },
    sourceHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    sourceName: {
        fontSize: 16,
        fontWeight: '600',
    },
    sourcePercentage: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    progressBar: {
        height: 4,
        borderRadius: 2,
        overflow: 'hidden',
    },
    progressFill: {
        height: '100%',
        borderRadius: 2,
    },
});

export default AnalyticsTestScreen;
