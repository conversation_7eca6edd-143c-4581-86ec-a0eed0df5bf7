// backend/src/controllers/chatController.ts

import { Request, Response, NextFunction } from 'express';
import { Types } from 'mongoose';
import Conversation from '../models/Conversation';
import Message from '../models/Message';
import Application from '../models/Application';

export const chatController = {
    // Initialize chat after job application
    async startConversation(req: Request, res: Response, next: NextFunction) {
        try {
            const { applicationId } = req.body;
            const userId = req.user?._id;

            if (!userId) {
                return res.status(401).json({ msg: 'User not authenticated' });
            }

            if (!applicationId) {
                return res.status(400).json({ msg: 'Application ID is required' });
            }

            // Find the application and verify user access
            const application = await Application.findById(applicationId)
                .populate('user', 'firstName lastName email role')
                .populate('employer', 'firstName lastName email role')
                .populate('job', 'title company');

            if (!application) {
                return res.status(404).json({ msg: 'Application not found' });
            }

            // Verify user is either the applicant or the employer
            const isApplicant = application.user._id.toString() === userId.toString();
            const isEmployer = application.employer._id.toString() === userId.toString();

            if (!isApplicant && !isEmployer) {
                return res.status(403).json({ msg: 'Access denied' });
            }

            // Check if conversation already exists
            let conversation = await Conversation.findOne({
                application: applicationId,
                jobSeeker: application.user._id,
                employer: application.employer._id
            }).populate([
                { path: 'jobSeeker', select: 'firstName lastName email role' },
                { path: 'employer', select: 'firstName lastName email role' },
                { path: 'job', select: 'title company' }
            ]);

            if (!conversation) {
                // Create new conversation
                conversation = new Conversation({
                    jobSeeker: application.user._id,
                    employer: application.employer._id,
                    application: applicationId,
                    job: application.job._id
                });

                await conversation.save();
                await conversation.populate([
                    { path: 'jobSeeker', select: 'firstName lastName email role' },
                    { path: 'employer', select: 'firstName lastName email role' },
                    { path: 'job', select: 'title company' }
                ]);
            }

            res.status(200).json({
                msg: 'Conversation ready',
                conversation
            });

        } catch (error: any) {
            console.error('Error starting conversation:', error);
            next(error);
        }
    },

    // Get user's conversation list
    async getConversations(req: Request, res: Response, next: NextFunction) {
        try {
            const userId = req.user?._id;
            const { page = 1, limit = 20 } = req.query;

            if (!userId) {
                return res.status(401).json({ msg: 'User not authenticated' });
            }

            const pageNum = parseInt(page as string);
            const limitNum = parseInt(limit as string);
            const skip = (pageNum - 1) * limitNum;

            // Find conversations where user is either job seeker or employer
            const conversations = await Conversation.find({
                $or: [
                    { jobSeeker: userId },
                    { employer: userId }
                ],
                isActive: true
            })
            .populate([
                { path: 'jobSeeker', select: 'firstName lastName email role profileImage' },
                { path: 'employer', select: 'firstName lastName email role profileImage' },
                { path: 'job', select: 'title company' },
                { path: 'application', select: 'status createdAt' }
            ])
            .sort({ updatedAt: -1 })
            .skip(skip)
            .limit(limitNum);

            // Get total count for pagination
            const totalConversations = await Conversation.countDocuments({
                $or: [
                    { jobSeeker: userId },
                    { employer: userId }
                ],
                isActive: true
            });

            res.status(200).json({
                conversations,
                pagination: {
                    currentPage: pageNum,
                    totalPages: Math.ceil(totalConversations / limitNum),
                    totalConversations,
                    hasNext: pageNum < Math.ceil(totalConversations / limitNum),
                    hasPrev: pageNum > 1
                }
            });

        } catch (error: any) {
            console.error('Error fetching conversations:', error);
            next(error);
        }
    },

    // Get conversation messages
    async getConversationMessages(req: Request, res: Response, next: NextFunction) {
        try {
            const { conversationId } = req.params;
            const userId = req.user?._id;
            const { page = 1, limit = 50 } = req.query;

            if (!userId) {
                return res.status(401).json({ msg: 'User not authenticated' });
            }

            // Verify user has access to this conversation
            const conversation = await Conversation.findOne({
                _id: conversationId,
                $or: [
                    { jobSeeker: userId },
                    { employer: userId }
                ]
            });

            if (!conversation) {
                return res.status(404).json({ msg: 'Conversation not found or access denied' });
            }

            const pageNum = parseInt(page as string);
            const limitNum = parseInt(limit as string);
            const skip = (pageNum - 1) * limitNum;

            // Get messages for this conversation
            const messages = await Message.find({
                conversation: conversationId,
                isDeleted: false
            })
            .populate('sender', 'firstName lastName email role profileImage')
            .populate('receiver', 'firstName lastName email role profileImage')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limitNum);

            // Get total count for pagination
            const totalMessages = await Message.countDocuments({
                conversation: conversationId,
                isDeleted: false
            });

            // Reverse messages to show oldest first
            messages.reverse();

            res.status(200).json({
                messages,
                pagination: {
                    currentPage: pageNum,
                    totalPages: Math.ceil(totalMessages / limitNum),
                    totalMessages,
                    hasNext: pageNum < Math.ceil(totalMessages / limitNum),
                    hasPrev: pageNum > 1
                }
            });

        } catch (error: any) {
            console.error('Error fetching conversation messages:', error);
            next(error);
        }
    },

    // Send message (REST endpoint as backup to Socket.IO)
    async sendMessage(req: Request, res: Response, next: NextFunction) {
        try {
            const { conversationId, content, messageType = 'text' } = req.body;
            const userId = req.user?._id;

            if (!userId) {
                return res.status(401).json({ msg: 'User not authenticated' });
            }

            if (!conversationId || !content) {
                return res.status(400).json({ msg: 'Conversation ID and content are required' });
            }

            // Verify conversation exists and user has access
            const conversation = await Conversation.findOne({
                _id: conversationId,
                $or: [
                    { jobSeeker: userId },
                    { employer: userId }
                ]
            });

            if (!conversation) {
                return res.status(404).json({ msg: 'Conversation not found or access denied' });
            }

            // Determine receiver
            const receiverId = conversation.jobSeeker.toString() === userId.toString() 
                ? conversation.employer 
                : conversation.jobSeeker;

            // Create new message
            const newMessage = new Message({
                conversation: conversationId,
                sender: userId,
                receiver: receiverId,
                content: content.trim(),
                messageType
            });

            await newMessage.save();
            await newMessage.populate('sender', 'firstName lastName email role profileImage');

            // Update conversation unread count
            const updateField = conversation.jobSeeker.toString() === userId.toString() 
                ? 'unreadCount.employer' 
                : 'unreadCount.jobSeeker';

            await Conversation.findByIdAndUpdate(conversationId, {
                $inc: { [updateField]: 1 }
            });

            res.status(201).json({
                msg: 'Message sent successfully',
                message: newMessage
            });

        } catch (error: any) {
            console.error('Error sending message:', error);
            next(error);
        }
    },

    // Mark messages as read
    async markMessagesAsRead(req: Request, res: Response, next: NextFunction) {
        try {
            const { conversationId } = req.params;
            const { messageIds } = req.body;
            const userId = req.user?._id;

            if (!userId) {
                return res.status(401).json({ msg: 'User not authenticated' });
            }

            // Verify conversation access
            const conversation = await Conversation.findOne({
                _id: conversationId,
                $or: [
                    { jobSeeker: userId },
                    { employer: userId }
                ]
            });

            if (!conversation) {
                return res.status(404).json({ msg: 'Conversation not found or access denied' });
            }

            // Mark messages as read
            const query: any = {
                conversation: conversationId,
                receiver: userId,
                isRead: false
            };

            if (messageIds && messageIds.length > 0) {
                query._id = { $in: messageIds };
            }

            const result = await Message.updateMany(query, {
                $set: { isRead: true },
                $push: {
                    readBy: {
                        user: userId,
                        readAt: new Date()
                    }
                }
            });

            // Reset unread count for this user
            const updateField = conversation.jobSeeker.toString() === userId.toString() 
                ? 'unreadCount.jobSeeker' 
                : 'unreadCount.employer';

            await Conversation.findByIdAndUpdate(conversationId, {
                $set: { [updateField]: 0 }
            });

            res.status(200).json({
                msg: 'Messages marked as read',
                modifiedCount: result.modifiedCount
            });

        } catch (error: any) {
            console.error('Error marking messages as read:', error);
            next(error);
        }
    },

    // Get total unread count for user
    async getUnreadCount(req: Request, res: Response, next: NextFunction) {
        try {
            const userId = req.user?._id;

            if (!userId) {
                return res.status(401).json({ msg: 'User not authenticated' });
            }

            // Get user's role to determine which unread count to use
            const userRole = req.user?.role;
            const unreadField = userRole === 'jobseeker' ? 'unreadCount.jobSeeker' : 'unreadCount.employer';

            // Convert userId to ObjectId if it's a string, otherwise use as is
            const userObjectId = typeof userId === 'string' ? new Types.ObjectId(userId) : userId;

            // Aggregate total unread count across all conversations
            const result = await Conversation.aggregate([
                {
                    $match: {
                        $or: [
                            { jobSeeker: userObjectId },
                            { employer: userObjectId }
                        ],
                        isActive: true
                    }
                },
                {
                    $group: {
                        _id: null,
                        totalUnread: {
                            $sum: userRole === 'jobseeker' ? '$unreadCount.jobSeeker' : '$unreadCount.employer'
                        }
                    }
                }
            ]);

            const totalUnread = result.length > 0 ? result[0].totalUnread : 0;

            res.status(200).json({
                totalUnread
            });

        } catch (error: any) {
            console.error('Error getting unread count:', error);
            next(error);
        }
    }
};
