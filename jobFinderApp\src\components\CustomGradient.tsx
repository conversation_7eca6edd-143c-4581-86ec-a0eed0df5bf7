// jobFinderApp/src/components/CustomGradient.tsx
// Custom gradient component using React Native's built-in components

import React from 'react';
import { View, ViewStyle } from 'react-native';

interface CustomGradientProps {
    colors: string[];
    style?: ViewStyle;
    children?: React.ReactNode;
    direction?: 'horizontal' | 'vertical' | 'diagonal';
}

const CustomGradient: React.FC<CustomGradientProps> = ({
    colors,
    style,
    children,
    direction = 'vertical'
}) => {
    // Create a gradient effect using multiple overlapping views
    const createGradientLayers = () => {
        if (colors.length < 2) {
            return (
                <View style={[{ backgroundColor: colors[0] || 'transparent' }, style]}>
                    {children}
                </View>
            );
        }

        const [startColor, endColor] = colors;

        return (
            <View style={[{ backgroundColor: startColor }, style]}>
                {/* Create multiple layers for smoother gradient effect */}
                <View
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: endColor,
                        opacity: 0.3,
                    }}
                />
                <View
                    style={[
                        {
                            position: 'absolute',
                            backgroundColor: endColor,
                            opacity: 0.4,
                        },
                        direction === 'vertical' && {
                            top: '40%',
                            left: 0,
                            right: 0,
                            bottom: 0,
                        },
                        direction === 'horizontal' && {
                            top: 0,
                            left: '40%',
                            right: 0,
                            bottom: 0,
                        },
                        direction === 'diagonal' && {
                            top: '20%',
                            left: '20%',
                            right: 0,
                            bottom: 0,
                        }
                    ]}
                />
                <View style={{ position: 'relative', zIndex: 1 }}>
                    {children}
                </View>
            </View>
        );
    };

    return createGradientLayers();
};

export default CustomGradient;
