{"name": "jobFinderApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "android:clean": "cd android && gradlew clean && cd ..", "android:release": "cd android && gradlew assembleRelease && cd ..", "android:bundle": "cd android && gradlew bundleRelease && cd ..", "android:install-release": "cd android && gradlew installRelease && cd ..", "optimize:all": "npm run android:clean && npm run optimize:assets && npm run android:release", "optimize:assets": "react-native optimize-assets", "analyze:size": "npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/ && dir android\\app\\src\\main\\assets\\index.android.bundle", "test:performance": "npm run android:release && echo 'Performance testing requires manual APK analysis'", "clean:cache": "npx react-native start --reset-cache", "clean:node": "rmdir /s /q node_modules && npm install", "clean:all": "npm run clean:cache && npm run clean:node && npm run android:clean"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-vector-icons/fontawesome": "^12.0.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/material-top-tabs": "^7.3.2", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@react-navigation/stack": "^7.3.3", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-config": "^1.5.5", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.25.0", "react-native-image-picker": "^8.2.1", "react-native-permissions": "^5.4.0", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.13", "@types/react": "^19.1.8", "@types/react-native-vector-icons": "^6.4.18", "@types/react-redux": "^7.1.34", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}