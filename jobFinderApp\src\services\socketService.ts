// jobFinderApp/src/services/socketService.ts

import { io, Socket } from 'socket.io-client';
import { API_BASE_URL } from '../config/env';
import { store } from '../redux/store';
import { 
    addMessage, 
    markMessagesAsRead, 
    updateTypingStatus,
    updateUserOnlineStatus,
    incrementUnreadCount
} from '../redux/chat/chatSlice';

class SocketService {
    private socket: Socket | null = null;
    private isConnected = false;
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private reconnectDelay = 1000;

    // Initialize socket connection
    connect(token: string): void {
        if (this.socket?.connected) {
            console.log('Socket already connected');
            return;
        }

        try {
            // Remove /api from the URL for Socket.IO connection
            const socketUrl = API_BASE_URL.replace('/api', '');
            console.log('🔌 [DEBUG] Connecting to Socket.IO server:', socketUrl);

            this.socket = io(socketUrl, {
                auth: {
                    token
                },
                transports: ['polling', 'websocket'], // Try polling first
                timeout: 20000,
                autoConnect: true,
                reconnection: true,
                reconnectionAttempts: 5,
                reconnectionDelay: 1000
            });

            this.setupEventListeners();
            console.log('Socket connection initiated');
        } catch (error) {
            console.error('Error initializing socket:', error);
        }
    }

    // Disconnect socket
    disconnect(): void {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
            this.isConnected = false;
            console.log('Socket disconnected');
        }
    }

    // Setup event listeners
    private setupEventListeners(): void {
        if (!this.socket) return;

        // Connection events
        this.socket.on('connect', () => {
            console.log('🔌 [SUCCESS] Socket connected successfully');
            console.log('🔌 [DEBUG] Socket ID:', this.socket?.id);
            this.isConnected = true;
            this.reconnectAttempts = 0;

            // Join user's personal room for notifications
            if (this.socket?.connected) {
                console.log('🔌 [DEBUG] Joining user room');
                this.socket.emit('join:user');
            }
        });

        this.socket.on('disconnect', (reason) => {
            console.log('Socket disconnected:', reason);
            this.isConnected = false;
            
            // Auto-reconnect logic
            if (reason === 'io server disconnect') {
                // Server initiated disconnect, don't reconnect
                return;
            }
            
            this.handleReconnection();
        });

        this.socket.on('connect_error', (error) => {
            console.error('🔌 [ERROR] Socket connection error:', error);
            console.error('🔌 [ERROR] Error details:', error.message);
            console.error('🔌 [ERROR] Error type:', error.type);
            this.handleReconnection();
        });

        // Chat events
        this.socket.on('new:message', (data: { message: any; conversationId: string }) => {
            console.log('New message received:', data);
            store.dispatch(addMessage({
                conversationId: data.conversationId,
                message: data.message
            }));
        });

        this.socket.on('message:notification', (data: { 
            conversationId: string; 
            message: any; 
            sender: any 
        }) => {
            console.log('Message notification received:', data);
            store.dispatch(incrementUnreadCount(data.conversationId));
            
            // You can add push notification logic here
            // showPushNotification(data.sender, data.message.content);
        });

        this.socket.on('messages:read', (data: { 
            conversationId: string; 
            readBy: string; 
            messageIds: string[] | 'all' 
        }) => {
            console.log('Messages marked as read:', data);
            store.dispatch(markMessagesAsRead({
                conversationId: data.conversationId,
                messageIds: data.messageIds === 'all' ? [] : data.messageIds
            }));
        });

        // Typing indicators
        this.socket.on('user:typing', (data: { userId: string; conversationId: string }) => {
            store.dispatch(updateTypingStatus({
                conversationId: data.conversationId,
                userId: data.userId,
                isTyping: true
            }));
        });

        this.socket.on('user:stopped_typing', (data: { userId: string; conversationId: string }) => {
            store.dispatch(updateTypingStatus({
                conversationId: data.conversationId,
                userId: data.userId,
                isTyping: false
            }));
        });

        // User presence
        this.socket.on('user:online', (data: { userId: string }) => {
            store.dispatch(updateUserOnlineStatus({
                userId: data.userId,
                isOnline: true
            }));
        });

        this.socket.on('user:offline', (data: { userId: string }) => {
            store.dispatch(updateUserOnlineStatus({
                userId: data.userId,
                isOnline: false
            }));
        });

        // Error handling
        this.socket.on('error', (error: { message: string }) => {
            console.error('Socket error:', error);
        });
    }

    // Handle reconnection logic
    private handleReconnection(): void {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnection attempts reached');
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
        
        setTimeout(() => {
            if (this.socket && !this.socket.connected) {
                this.socket.connect();
            }
        }, delay);
    }

    // Join conversation room
    joinConversation(conversationId: string): void {
        if (this.socket?.connected) {
            this.socket.emit('join:conversation', { conversationId });
            console.log(`Joined conversation: ${conversationId}`);
        }
    }

    // Leave conversation room
    leaveConversation(conversationId: string): void {
        if (this.socket?.connected) {
            this.socket.emit('leave:conversation', { conversationId });
            console.log(`Left conversation: ${conversationId}`);
        }
    }

    // Send message
    sendMessage(conversationId: string, content: string, messageType: 'text' | 'image' | 'file' = 'text'): void {
        if (this.socket?.connected) {
            this.socket.emit('send:message', {
                conversationId,
                content,
                messageType
            });
            console.log(`Message sent to conversation: ${conversationId}`);
        } else {
            console.error('Socket not connected, cannot send message');
        }
    }

    // Mark messages as read
    markAsRead(conversationId: string, messageIds?: string[]): void {
        if (this.socket?.connected) {
            this.socket.emit('mark:read', {
                conversationId,
                messageIds
            });
            console.log(`Messages marked as read in conversation: ${conversationId}`);
        }
    }

    // Typing indicators
    startTyping(conversationId: string): void {
        if (this.socket?.connected) {
            this.socket.emit('typing:start', { conversationId });
        }
    }

    stopTyping(conversationId: string): void {
        if (this.socket?.connected) {
            this.socket.emit('typing:stop', { conversationId });
        }
    }

    // Check connection status
    isSocketConnected(): boolean {
        return this.isConnected && this.socket?.connected === true;
    }

    // Get socket ID
    getSocketId(): string | undefined {
        return this.socket?.id;
    }
}

// Export singleton instance
export const socketService = new SocketService();
export default socketService;
