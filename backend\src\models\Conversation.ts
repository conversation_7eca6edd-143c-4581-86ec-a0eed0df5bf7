// backend/src/models/Conversation.ts

import { Schema, model, Document, Types } from 'mongoose';

export interface IConversation extends Document {
    _id: Types.ObjectId;
    jobSeeker: Types.ObjectId;
    employer: Types.ObjectId;
    application: Types.ObjectId;
    job: Types.ObjectId;
    lastMessage?: {
        content: string;
        sender: Types.ObjectId;
        timestamp: Date;
    };
    unreadCount: {
        jobSeeker: number;
        employer: number;
    };
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}

const ConversationSchema = new Schema<IConversation>({
    jobSeeker: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    employer: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    application: {
        type: Schema.Types.ObjectId,
        ref: 'Application',
        required: true
    },
    job: {
        type: Schema.Types.ObjectId,
        ref: 'Job',
        required: true
    },
    lastMessage: {
        content: { type: String },
        sender: { type: Schema.Types.ObjectId, ref: 'User' },
        timestamp: { type: Date }
    },
    unreadCount: {
        jobSeeker: { type: Number, default: 0 },
        employer: { type: Number, default: 0 }
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});

// Indexes for efficient querying
ConversationSchema.index({ jobSeeker: 1, employer: 1, application: 1 }, { unique: true });
ConversationSchema.index({ jobSeeker: 1, updatedAt: -1 });
ConversationSchema.index({ employer: 1, updatedAt: -1 });
ConversationSchema.index({ application: 1 });

// Virtual populate for job seeker details
ConversationSchema.virtual('jobSeekerDetails', {
    ref: 'User',
    localField: 'jobSeeker',
    foreignField: '_id',
    justOne: true
});

// Virtual populate for employer details
ConversationSchema.virtual('employerDetails', {
    ref: 'User',
    localField: 'employer',
    foreignField: '_id',
    justOne: true
});

// Virtual populate for job details
ConversationSchema.virtual('jobDetails', {
    ref: 'Job',
    localField: 'job',
    foreignField: '_id',
    justOne: true
});

// Virtual populate for application details
ConversationSchema.virtual('applicationDetails', {
    ref: 'Application',
    localField: 'application',
    foreignField: '_id',
    justOne: true
});

// Ensure virtual fields are serialized
ConversationSchema.set('toJSON', { virtuals: true });
ConversationSchema.set('toObject', { virtuals: true });

const Conversation = model<IConversation>('Conversation', ConversationSchema);

export default Conversation;
