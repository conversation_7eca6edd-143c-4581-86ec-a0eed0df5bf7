// jobFinderApp/src/navigation/EmployerStack.tsx

import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import EmployerTabNavigator from './EmployerTabNavigator';

// Import employer screens
import PostJobScreen from '../screens/employer/PostJobScreen';
import EditJobScreen from '../screens/employer/EditJobScreen';
import JobApplicationsScreen from '../screens/employer/JobApplicationsScreen';
import ApplicationDetailScreen from '../screens/employer/ApplicationDetailScreen';
import CompanyEditScreen from '../screens/employer/CompanyEditScreen';
import AnalyticsScreen from '../screens/employer/AnalyticsScreen';
import PremiumScreen from '../screens/employer/PremiumScreen';
import PasswordChangeScreen from '../screens/settings/PasswordChangeScreen';
import JobTemplatesScreen from '../screens/employer/JobTemplatesScreen';
import EmailAutomationScreen from '../screens/employer/EmailAutomationScreen';
import InterviewSchedulingScreen from '../screens/employer/InterviewSchedulingScreen';
import ChatListScreen from '../screens/chat/ChatListScreen';
import ChatDetailScreen from '../screens/chat/ChatDetailScreen';

export type EmployerStackParamList = {
    EmployerTabs: undefined;
    PostJob: { templateData?: any } | undefined;
    EditJob: { jobId: string };
    JobApplications: { jobId: string; jobTitle: string };
    ApplicationDetail: { applicationId: string };
    CompanyEdit: undefined;
    Analytics: undefined;
    Premium: undefined;
    PasswordChange: undefined;
    JobTemplates: undefined;
    CreateTemplate: undefined;
    TemplatePreview: { templateId: string };
    EmailAutomation: undefined;
    CreateEmailTemplate: undefined;
    EditEmailTemplate: { templateId: string };
    CreateEmailCampaign: undefined;
    CreateAutomationRule: undefined;
    InterviewScheduling: { applicationId?: string } | undefined;
    InterviewDetail: { interviewId: string };
    // Chat screens
    ChatList: undefined;
    ChatDetail: { conversationId: string };
};

const Stack = createNativeStackNavigator<EmployerStackParamList>();

const EmployerStack: React.FC = () => {
    return (
        <Stack.Navigator
            screenOptions={{
                headerShown: false,
            }}
        >
            <Stack.Screen 
                name="EmployerTabs" 
                component={EmployerTabNavigator} 
            />
            <Stack.Screen 
                name="PostJob" 
                component={PostJobScreen}
                options={{
                    headerShown: true,
                    title: 'Post New Job',
                    presentation: 'modal',
                }}
            />
            <Stack.Screen 
                name="EditJob" 
                component={EditJobScreen}
                options={{
                    headerShown: true,
                    title: 'Edit Job',
                }}
            />
            <Stack.Screen 
                name="JobApplications" 
                component={JobApplicationsScreen}
                options={({ route }) => ({
                    headerShown: true,
                    title: `${route.params.jobTitle} - Applications`,
                })}
            />
            <Stack.Screen 
                name="ApplicationDetail" 
                component={ApplicationDetailScreen}
                options={{
                    headerShown: true,
                    title: 'Application Details',
                }}
            />
            <Stack.Screen 
                name="CompanyEdit" 
                component={CompanyEditScreen}
                options={{
                    headerShown: true,
                    title: 'Edit Company Profile',
                }}
            />
            <Stack.Screen 
                name="Analytics" 
                component={AnalyticsScreen}
                options={{
                    headerShown: true,
                    title: 'Analytics',
                }}
            />
            <Stack.Screen
                name="Premium"
                component={PremiumScreen}
                options={{
                    headerShown: true,
                    title: 'Premium Features',
                    presentation: 'modal',
                }}
            />
            <Stack.Screen
                name="PasswordChange"
                component={PasswordChangeScreen}
                options={{
                    headerShown: true,
                    title: 'Change Password',
                }}
            />
            <Stack.Screen
                name="JobTemplates"
                component={JobTemplatesScreen}
                options={{
                    headerShown: true,
                    title: 'Job Templates',
                }}
            />
            <Stack.Screen
                name="EmailAutomation"
                component={EmailAutomationScreen}
                options={{
                    headerShown: true,
                    title: 'Email Automation',
                }}
            />
            <Stack.Screen
                name="InterviewScheduling"
                component={InterviewSchedulingScreen}
                options={{
                    headerShown: true,
                    title: 'Interview Scheduling',
                }}
            />
            <Stack.Screen
                name="ChatList"
                component={ChatListScreen}
                options={{ headerShown: true, title: 'Messages' }}
            />
            <Stack.Screen
                name="ChatDetail"
                component={ChatDetailScreen}
                options={{ headerShown: true, title: 'Chat' }}
            />
        </Stack.Navigator>
    );
};

export default EmployerStack;
