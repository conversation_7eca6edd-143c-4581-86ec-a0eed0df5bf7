// jobFinderApp/src/screens/employer/__tests__/AnalyticsScreen.test.tsx
// Test file for the enhanced Analytics Screen

import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import AnalyticsScreen from '../AnalyticsScreen';
import { ThemeProvider } from '../../../contexts/ThemeContext';

// Mock the analytics slice
const mockAnalyticsSlice = {
    name: 'analytics',
    initialState: {
        analytics: {
            overview: {
                total_jobs: 15,
                active_jobs: 8,
                total_applications: 142,
                total_views: 1250,
                avg_conversion_rate: 11.4
            },
            top_jobs: [
                {
                    id: '1',
                    title: 'Senior React Developer',
                    applications: 45,
                    views: 320,
                    conversion_rate: 14.1
                }
            ],
            application_sources: [
                {
                    source: 'LinkedIn',
                    count: 85,
                    percentage: 60,
                    quality_score: 4.2
                }
            ],
            hiring_funnel: [],
            recommendations: []
        },
        loading: false,
        error: null,
        selectedPeriod: '30d'
    },
    reducers: {
        setPeriod: (state: any, action: any) => {
            state.selectedPeriod = action.payload;
        }
    }
};

// Create a mock store
const createMockStore = () => {
    return configureStore({
        reducer: {
            analytics: () => mockAnalyticsSlice.initialState
        }
    });
};

// Mock the fetch function
jest.mock('../../../redux/employer/analyticsSlice', () => ({
    fetchCompanyAnalytics: jest.fn(() => ({ type: 'analytics/fetchCompanyAnalytics' })),
    setPeriod: jest.fn(() => ({ type: 'analytics/setPeriod' }))
}));

describe('AnalyticsScreen', () => {
    let store: any;

    beforeEach(() => {
        store = createMockStore();
    });

    const renderWithProviders = (component: React.ReactElement) => {
        return render(
            <Provider store={store}>
                <ThemeProvider>
                    {component}
                </ThemeProvider>
            </Provider>
        );
    };

    it('renders analytics dashboard title', () => {
        const { getByText } = renderWithProviders(<AnalyticsScreen />);
        expect(getByText('Analytics Dashboard')).toBeTruthy();
    });

    it('displays overview metrics correctly', () => {
        const { getByText } = renderWithProviders(<AnalyticsScreen />);
        
        expect(getByText('15')).toBeTruthy(); // Total jobs
        expect(getByText('142')).toBeTruthy(); // Total applications
        expect(getByText('1,250')).toBeTruthy(); // Total views
        expect(getByText('11.4%')).toBeTruthy(); // Conversion rate
    });

    it('shows trend indicators for metrics', () => {
        const { getAllByTestId } = renderWithProviders(<AnalyticsScreen />);
        
        // Should have trend indicators (up/down arrows)
        const trendElements = getAllByTestId(/trend-/);
        expect(trendElements.length).toBeGreaterThan(0);
    });

    it('displays top performing jobs', () => {
        const { getByText } = renderWithProviders(<AnalyticsScreen />);
        expect(getByText('Senior React Developer')).toBeTruthy();
    });

    it('shows application sources with quality scores', () => {
        const { getByText } = renderWithProviders(<AnalyticsScreen />);
        expect(getByText('LinkedIn')).toBeTruthy();
        expect(getByText('60%')).toBeTruthy();
    });

    it('handles refresh functionality', () => {
        const { getByTestId } = renderWithProviders(<AnalyticsScreen />);

        // Find the ScrollView with RefreshControl
        const scrollView = getByTestId('analytics-scroll-view');

        // Simulate pull-to-refresh
        fireEvent(scrollView, 'refresh');

        // Should trigger analytics fetch
        expect(store.getActions()).toContainEqual(
            expect.objectContaining({ type: 'analytics/fetchCompanyAnalytics' })
        );
    });

    it('renders lists with proper key props', () => {
        const { container } = renderWithProviders(<AnalyticsScreen />);

        // This test ensures that React doesn't throw key prop warnings
        // The test will pass if no console warnings are generated
        expect(container).toBeTruthy();
    });
});

export default {};
