{"name": "react-native-image-picker", "version": "8.2.1", "description": "A React Native module that allows you to use native UI to select media from the device library or directly from the camera", "react-native": "src/index.ts", "main": "src/index.ts", "types": "lib/typescript/index.d.ts", "files": ["/android", "!/android/build", "/ios", "/*.podspec", "/src", "/lib"], "author": "<PERSON> (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "homepage": "https://github.com/react-native-image-picker/react-native-image-picker", "license": "MIT", "scripts": {"start": "react-native start", "example": "yarn --cwd example", "prepare": "bob build", "ci:publish": "yarn semantic-release"}, "keywords": ["react-native", "react-native-image-picker", "react", "native", "image", "picker"], "peerDependencies": {"react": "*", "react-native": "*"}, "repository": {"type": "git", "url": "https://github.com/react-native-image-picker/react-native-image-picker.git"}, "devDependencies": {"@react-native-community/bob": "0.17.1", "@semantic-release/git": "10.0.1", "@types/react": "18.2.13", "@types/react-native": "0.72.2", "semantic-release": "21.0.5", "typescript": "5.2.2"}, "@react-native-community/bob": {"source": "src", "output": "lib", "targets": ["typescript"]}, "codegenConfig": {"name": "RNImagePickerSpec", "type": "modules", "jsSrcsDir": "src", "android": {"javaPackageName": "com.imagepicker"}}}