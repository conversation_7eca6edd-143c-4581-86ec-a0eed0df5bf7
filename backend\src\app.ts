// backend/src/app.ts
import express from 'express';
import cors from 'cors';
import authRoutes from './routes/authRoutes';
import jobRoutes from './routes/jobRoutes';
import userRoutes from './routes/userRoutes'; // Import user routes
import employerRoutes from './routes/employerRoutes'; // Import employer routes
import applyRoutes from './routes/applyRoutes'; // Import application routes
import viewRoutes from './routes/viewRoutes'; // Import view tracking routes
import chatRoutes from './routes/chatRoutes'; // Import chat routes
import './models/Job'; // Ensure Job model is registered with Mongoose
import './models/Company'; // Ensure Company model is registered with Mongoose

const app = express();

app.use(express.json());
app.use(cors());

app.get('/', (req, res) => {
    res.send('JobFinder Backend API is running!');
});

// Authentication routes
app.use('/api/auth', authRoutes);
// Job routes (some are protected in jobRoutes.ts directly)
app.use('/api/jobs', jobRoutes);
// User-specific routes (protected)
app.use('/api/users', userRoutes); // Add user routes
// Employer-specific routes (protected)
app.use('/api/employers', employerRoutes); // Add employer routes
// Application routes (protected)
app.use('/api/applications', applyRoutes); // Add application routes
// View tracking routes (protected)
app.use('/api/views', viewRoutes); // Add view tracking routes
// Chat routes (protected)
app.use('/api/chat', chatRoutes); // Add chat routes

export default app;