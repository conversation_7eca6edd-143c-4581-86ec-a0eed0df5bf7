import React, { useRef } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Button, Image, Alert } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { AppStackParamList } from '../../types/navigation';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../redux/store';
import { fetchJobById, clearCurrentJob } from '../../redux/jobs/jobSlice';
import { useEffect } from 'react';
import { t } from '../../i18n';
import { useTheme } from '../../contexts/ThemeContext';
import CompanyInfoSection from '../../components/CompanyInfoSection';
import { viewService, ViewDurationTracker, getViewSource } from '../../services/viewService';

// Define the type for the route props specific to this screen
type JobDetailScreenRouteProp = RouteProp<AppStackParamList, 'JobDetail'>;

// Category icons mapping
const CATEGORY_ICONS: Record<string, string> = {
    Technology: '💻',
    Design: '🎨',
    Marketing: '📈',
    Sales: '💼',
    Finance: '💰',
    Healthcare: '🩺',
    Engineering: '🛠️',
    Education: '📚',
    Operations: '⚙️',
    'Human Resources': '👥',
    'Customer Service': '🎧',
    Legal: '⚖️',
    Consulting: '💡',
    Manufacturing: '🏭',
    Retail: '🛍️',
    // Add more as needed
};

// Helper function to get category icon
const getCategoryIcon = (category: string): string => {
    return CATEGORY_ICONS[category] || '📁';
};

const JobDetailScreen: React.FC = () => {
    const route = useRoute<JobDetailScreenRouteProp>();
    const navigation = useNavigation<NativeStackNavigationProp<AppStackParamList>>();
    const dispatch = useDispatch<AppDispatch>();
    const { currentJob, currentJobLoading, currentJobError } = useSelector((state: RootState) => state.jobs);
    const { user } = useSelector((state: RootState) => state.auth);
    const { theme } = useTheme();
    const isGuest = user?._id === 'guest-user';
    const { jobId } = route.params;

    // View tracking
    const viewTrackerRef = useRef<ViewDurationTracker | null>(null);
    const hasTrackedView = useRef(false);

    useEffect(() => {
        if (jobId) {
            dispatch(fetchJobById(jobId));
        }
        return () => {
            dispatch(clearCurrentJob());
        };
    }, [dispatch, jobId]);

    // Track view when job loads and user is not the owner
    useEffect(() => {
        if (currentJob && user && !isGuest && !hasTrackedView.current) {
            // Don't track if user is the job owner (check if postedBy exists and matches)
            const isOwner = (currentJob as any).postedBy === user._id ||
                           (currentJob as any).postedBy?._id === user._id;
            if (!isOwner) {
                trackJobView();
                hasTrackedView.current = true;
            }
        }
    }, [currentJob, user, isGuest]);

    // Start view duration tracking when job loads
    useEffect(() => {
        if (currentJob && user && !isGuest && hasTrackedView.current) {
            viewTrackerRef.current = new ViewDurationTracker();
        }

        return () => {
            // Send view duration when component unmounts
            if (viewTrackerRef.current && hasTrackedView.current) {
                const duration = viewTrackerRef.current.stop();
                updateViewDuration(duration);
            }
        };
    }, [currentJob, user, isGuest]);

    const trackJobView = async () => {
        try {
            const source = getViewSource(); // Will default to 'direct' in React Native
            await viewService.trackJobView(jobId, {
                source,
                referrer: 'mobile-app'
            });
        } catch (error) {
            console.error('Error tracking job view:', error);
        }
    };

    const updateViewDuration = async (duration: number) => {
        try {
            // You could implement a separate endpoint to update view duration
            // For now, we'll track it in the initial view call
            console.log(`View duration: ${duration} seconds`);
        } catch (error) {
            console.error('Error updating view duration:', error);
        }
    };

    // Helper function to format view count
    const formatViewCount = (count: number): string => {
        if (count < 1000) {
            return count.toString();
        } else if (count < 1000000) {
            return (count / 1000).toFixed(1) + 'K';
        } else {
            return (count / 1000000).toFixed(1) + 'M';
        }
    };

    if (currentJobLoading) {
        return (
            <View style={[styles.centeredContainer, { backgroundColor: theme.background }]}>
                <Text style={[styles.loadingText, { color: theme.textSecondary }]}>{t('loadingJobDetails')}</Text>
            </View>
        );
    }

    if (currentJobError || !currentJob) {
        return (
            <View style={[styles.centeredContainer, { backgroundColor: theme.background }]}>
                <Text style={[styles.errorText, { color: theme.error }]}>{t('jobNotFound')}</Text>
                <Button title={t('cancel')} onPress={() => navigation.goBack()} />
            </View>
        );
    }

    return (
        <View style={[styles.root, { backgroundColor: theme.background }]}>
            {/* Header */}
            <View style={[styles.header, { backgroundColor: theme.surface, borderBottomColor: theme.border }]}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Ionicons name="arrow-back" size={24} color={theme.text} />
                </TouchableOpacity>
                <Text style={[styles.headerTitle, { color: theme.text }]}>{t('jobDetails') || 'Job Details'}</Text>
            </View>

            <ScrollView contentContainerStyle={styles.scrollContent}>
                {/* Job Title & Info */}
                <Text style={[styles.title, { color: theme.text }]}>{currentJob.title}</Text>
                <Text style={[styles.companyLocation, { color: theme.textSecondary }]}>
                    {currentJob.company} · {currentJob.location.humanReadable}
                </Text>

                <View style={styles.jobMetaRow}>
                    <Text style={[styles.postedDate, { color: theme.textMuted }]}>
                        Posted {formatPostedDate(currentJob.postedDate)}
                    </Text>

                    {/* View Count */}
                    {(currentJob as any).views !== undefined && (
                        <View style={styles.viewCountContainer}>
                            <Ionicons name="eye-outline" size={16} color={theme.textMuted} />
                            <Text style={[styles.viewCount, { color: theme.textMuted }]}>
                                {formatViewCount((currentJob as any).views || 0)} views
                            </Text>
                        </View>
                    )}
                </View>

                {/* Modern Category Section */}
                <View style={styles.categorySection}>
                    <View style={[styles.categoryChip, { backgroundColor: theme.primary + '15', borderColor: theme.primary + '30' }]}>
                        <Text style={styles.categoryIcon}>{getCategoryIcon(currentJob.category)}</Text>
                        <Text style={[styles.categoryText, { color: theme.primary }]}>{currentJob.category}</Text>
                    </View>

                    {/* Job Type Chip */}
                    {currentJob.jobType && (
                        <View style={[styles.jobTypeChip, { backgroundColor: theme.surface, borderColor: theme.border }]}>
                            <Ionicons name="briefcase-outline" size={14} color={theme.textSecondary} />
                            <Text style={[styles.jobTypeText, { color: theme.textSecondary }]}>{currentJob.jobType}</Text>
                        </View>
                    )}

                    {/* Salary Range Chip */}
                    {currentJob.salaryRange && (
                        <View style={[styles.salaryChip, { backgroundColor: theme.success + '15', borderColor: theme.success + '30' }]}>
                            <Ionicons name="cash-outline" size={14} color={theme.success} />
                            <Text style={[styles.salaryText, { color: theme.success }]}>
                                ${currentJob.salaryRange.min.toLocaleString()} - ${currentJob.salaryRange.max.toLocaleString()}
                            </Text>
                        </View>
                    )}

                    {/* Experience Level Chip */}
                    {(currentJob as any).experienceLevel && (
                        <View style={[styles.experienceChip, { backgroundColor: theme.warning + '15', borderColor: theme.warning + '30' }]}>
                            <Ionicons name="school-outline" size={14} color={theme.warning} />
                            <Text style={[styles.experienceText, { color: theme.warning }]}>{(currentJob as any).experienceLevel}</Text>
                        </View>
                    )}

                    {/* Location Chip */}
                    <View style={[styles.locationChip, { backgroundColor: theme.secondary + '15', borderColor: theme.secondary + '30' }]}>
                        <Ionicons name="location-outline" size={14} color={theme.secondary} />
                        <Text style={[styles.locationText, { color: theme.secondary }]}>{currentJob.location.humanReadable}</Text>
                    </View>
                </View>

                {/* Job Description */}
                <Text style={[styles.sectionTitle, { color: theme.text }]}>{t('jobDescription') || 'Job Description'}</Text>
                <Text style={[styles.description, { color: theme.textSecondary }]}>{currentJob.description}</Text>

                {/* Requirements */}
                {currentJob.qualifications && currentJob.qualifications.length > 0 && (
                    <>
                        <Text style={[styles.sectionTitle, { color: theme.text }]}>{t('requirements') || 'Requirements'}</Text>
                        {currentJob.qualifications.map((req: string, idx: number) => {
                            return React.createElement(View, {
                                key: `requirement-${idx}`,
                                style: styles.requirementRow
                            }, [
                                React.createElement(Ionicons, {
                                    key: 'icon',
                                    name: 'checkbox-outline',
                                    size: 20,
                                    color: theme.success
                                }),
                                React.createElement(Text, {
                                    key: 'text',
                                    style: [styles.requirementText, { color: theme.textSecondary }]
                                }, req)
                            ]);
                        })}
                    </>
                )}

                {/* Company Info */}
                <Text style={[styles.sectionTitle, { color: theme.text }]}>{t('companyInfo') || 'Company Info'}</Text>
                {currentJob.companyId && typeof currentJob.companyId === 'object' ? (
                    <CompanyInfoSection
                        companyInfo={currentJob.companyId}
                        fallbackCompanyName={currentJob.company}
                    />
                ) : (
                    <View style={[styles.companyInfo, { backgroundColor: theme.surface }]}>
                        <Image
                            source={require('../../../assets/company_logo_placeholder.png')}
                            style={styles.companyLogo}
                        />
                        <View style={styles.companyDetails}>
                            <Text style={[styles.companyName, { color: theme.text }]}>{currentJob.company}</Text>
                            <View style={styles.companyMetaRow}>
                                <View style={[styles.companyIndustryChip, { backgroundColor: theme.primary + '15' }]}>
                                    <Text style={styles.companyIndustryIcon}>{getCategoryIcon(currentJob.category)}</Text>
                                    <Text style={[styles.companyIndustry, { color: theme.primary }]}>{currentJob.category}</Text>
                                </View>
                            </View>
                        </View>
                    </View>
                )}
            </ScrollView>

            {/* Bottom Buttons */}
            <View style={[styles.bottomBar, { backgroundColor: theme.surface, borderTopColor: theme.border }]}>
                <TouchableOpacity
                    style={[
                        styles.applyButton,
                        { backgroundColor: theme.primary },
                        isGuest && { backgroundColor: theme.textMuted }
                    ]}
                    onPress={() => {
                        if (isGuest) {
                            Alert.alert('Login required', 'Login required to apply for this job.');
                        } else {
                            navigation.navigate('Application', { jobId });
                        }
                    }}
                    disabled={isGuest}
                >
                    <Text style={styles.applyButtonText}>{t('applyNow') || 'Apply Now'}</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    root: { flex: 1 },
    header: { flexDirection: 'row', alignItems: 'center', padding: 16, borderBottomWidth: 1 },
    headerTitle: { fontSize: 18, fontWeight: 'bold', marginLeft: 16 },
    scrollContent: { padding: 20, paddingBottom: 100 },
    title: { fontSize: 22, fontWeight: 'bold', marginBottom: 4 },
    companyLocation: { fontSize: 16, marginBottom: 2 },
    jobMetaRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16
    },
    postedDate: { fontSize: 13 },
    viewCountContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    viewCount: {
        fontSize: 13,
        marginLeft: 4,
    },
    sectionTitle: { fontSize: 16, fontWeight: 'bold', marginTop: 20, marginBottom: 8 },
    description: { fontSize: 15, marginBottom: 12 },
    requirementRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 6 },
    requirementText: { marginLeft: 8, fontSize: 15 },
    companyInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
        padding: 16,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    companyLogo: { width: 48, height: 48, borderRadius: 24, marginRight: 12, backgroundColor: '#eaeaea' },
    companyName: { fontSize: 16, fontWeight: 'bold' },
    companyIndustry: { fontSize: 14 },
    uploadBox: { borderWidth: 1, borderColor: '#e0e0e0', borderRadius: 8, padding: 16, alignItems: 'center', marginBottom: 20, backgroundColor: '#fafafa' },
    uploadText: { fontSize: 14, color: '#888', marginBottom: 10 },
    uploadButton: { backgroundColor: '#e0e0e0', borderRadius: 6, paddingHorizontal: 20, paddingVertical: 8 },
    uploadButtonText: { color: '#222', fontWeight: 'bold' },
    bottomBar: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', padding: 16, borderTopWidth: 1 },
    applyButton: { flex: 1, borderRadius: 8, paddingVertical: 14, alignItems: 'center', marginRight: 8 },
    applyButtonText: { color: '#fff', fontWeight: 'bold', fontSize: 16 },
    centeredContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' },
    loadingText: { marginTop: 10, fontSize: 16 },
    errorText: { fontSize: 18, textAlign: 'center', marginVertical: 20 },

    // Modern Category Section Styles
    categorySection: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginBottom: 20,
        gap: 8,
    },
    categoryChip: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 20,
        borderWidth: 1,
        marginRight: 8,
        marginBottom: 8,
    },
    categoryIcon: {
        fontSize: 16,
        marginRight: 6,
    },
    categoryText: {
        fontSize: 14,
        fontWeight: '600',
    },
    jobTypeChip: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        paddingVertical: 6,
        borderRadius: 16,
        borderWidth: 1,
        marginRight: 8,
        marginBottom: 8,
    },
    jobTypeText: {
        fontSize: 12,
        fontWeight: '500',
        marginLeft: 4,
    },
    salaryChip: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        paddingVertical: 6,
        borderRadius: 16,
        borderWidth: 1,
        marginRight: 8,
        marginBottom: 8,
    },
    salaryText: {
        fontSize: 12,
        fontWeight: '600',
        marginLeft: 4,
    },

    // Enhanced Company Section Styles
    companyDetails: {
        flex: 1,
    },
    companyMetaRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 4,
    },
    companyIndustryChip: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    companyIndustryIcon: {
        fontSize: 12,
        marginRight: 4,
    },

    // Additional Chip Styles
    experienceChip: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        paddingVertical: 6,
        borderRadius: 16,
        borderWidth: 1,
        marginRight: 8,
        marginBottom: 8,
    },
    experienceText: {
        fontSize: 12,
        fontWeight: '500',
        marginLeft: 4,
    },
    locationChip: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        paddingVertical: 6,
        borderRadius: 16,
        borderWidth: 1,
        marginRight: 8,
        marginBottom: 8,
    },
    locationText: {
        fontSize: 12,
        fontWeight: '500',
        marginLeft: 4,
    },
});

function formatPostedDate(dateString: string) {
    // Example: "Posted 2 days ago"
    const date = new Date(dateString);
    const now = new Date();
    const diff = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    if (diff === 0) return 'today';
    if (diff === 1) return '1 day ago';
    return `${diff} days ago`;
}

export default JobDetailScreen;