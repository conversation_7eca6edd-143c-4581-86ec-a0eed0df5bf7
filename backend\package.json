{"name": "jobfinderbackend", "version": "1.0.0", "main": "server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec ts-node server.ts", "seed": "ts-node seed.ts", "seed:destroy": "ts-node seed.ts -d"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/multer": "^2.0.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.3", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.16.1", "multer": "^2.0.2", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^7.0.3", "react-native-image-picker": "^8.2.1", "socket.io": "^4.8.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.3", "@types/cors": "^2.8.18", "@types/express": "^4.17.22", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.97", "@types/node": "^20.19.4", "@types/nodemailer": "^6.4.17", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "type": "commonjs", "description": ""}