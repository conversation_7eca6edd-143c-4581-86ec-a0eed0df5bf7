// backend/src/models/Message.ts

import { Schema, model, Document, Types } from 'mongoose';

export interface IMessage extends Document {
    _id: Types.ObjectId;
    conversation: Types.ObjectId;
    sender: Types.ObjectId;
    receiver: Types.ObjectId;
    content: string;
    messageType: 'text' | 'image' | 'file';
    attachments?: {
        url: string;
        filename: string;
        fileType: string;
        fileSize: number;
    }[];
    readBy: {
        user: Types.ObjectId;
        readAt: Date;
    }[];
    isRead: boolean;
    isEdited: boolean;
    editedAt?: Date;
    isDeleted: boolean;
    deletedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}

const MessageSchema = new Schema<IMessage>({
    conversation: {
        type: Schema.Types.ObjectId,
        ref: 'Conversation',
        required: true
    },
    sender: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    receiver: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    content: {
        type: String,
        required: true,
        maxlength: [2000, 'Message content cannot exceed 2000 characters']
    },
    messageType: {
        type: String,
        enum: ['text', 'image', 'file'],
        default: 'text'
    },
    attachments: [{
        url: { type: String, required: true },
        filename: { type: String, required: true },
        fileType: { type: String, required: true },
        fileSize: { type: Number, required: true }
    }],
    readBy: [{
        user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
        readAt: { type: Date, required: true }
    }],
    isRead: {
        type: Boolean,
        default: false
    },
    isEdited: {
        type: Boolean,
        default: false
    },
    editedAt: {
        type: Date
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    deletedAt: {
        type: Date
    }
}, {
    timestamps: true
});

// Indexes for efficient querying
MessageSchema.index({ conversation: 1, createdAt: -1 });
MessageSchema.index({ sender: 1, createdAt: -1 });
MessageSchema.index({ receiver: 1, isRead: 1 });
MessageSchema.index({ conversation: 1, isDeleted: 1, createdAt: -1 });

// Virtual populate for sender details
MessageSchema.virtual('senderDetails', {
    ref: 'User',
    localField: 'sender',
    foreignField: '_id',
    justOne: true
});

// Virtual populate for receiver details
MessageSchema.virtual('receiverDetails', {
    ref: 'User',
    localField: 'receiver',
    foreignField: '_id',
    justOne: true
});

// Pre-save middleware to update conversation's lastMessage
MessageSchema.pre('save', async function(next) {
    if (this.isNew && !this.isDeleted) {
        try {
            const Conversation = model('Conversation');
            await Conversation.findByIdAndUpdate(this.conversation, {
                lastMessage: {
                    content: this.content,
                    sender: this.sender,
                    timestamp: this.createdAt || new Date()
                },
                updatedAt: new Date()
            });
        } catch (error) {
            console.error('Error updating conversation lastMessage:', error);
        }
    }
    next();
});

// Ensure virtual fields are serialized
MessageSchema.set('toJSON', { virtuals: true });
MessageSchema.set('toObject', { virtuals: true });

const Message = model<IMessage>('Message', MessageSchema);

export default Message;
