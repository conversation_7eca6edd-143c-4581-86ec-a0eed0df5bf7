// jobFinderApp/src/components/chat/MessageBubble.tsx

import React from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import { Message, User } from '../../redux/chat/chatSlice';

interface MessageBubbleProps {
    message: Message;
    isOwnMessage: boolean;
    showTimestamp?: boolean;
    onLongPress?: () => void;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
    message,
    isOwnMessage,
    showTimestamp = false,
    onLongPress
}) => {
    const { theme } = useTheme();

    const formatTime = (dateString: string): string => {
        const date = new Date(dateString);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    const formatDate = (dateString: string): string => {
        const date = new Date(dateString);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        if (date.toDateString() === today.toDateString()) {
            return 'Today';
        } else if (date.toDateString() === yesterday.toDateString()) {
            return 'Yesterday';
        } else {
            return date.toLocaleDateString();
        }
    };

    const bubbleStyle = [
        styles.messageBubble,
        isOwnMessage 
            ? [styles.ownMessage, { backgroundColor: theme.primary }]
            : [styles.otherMessage, { backgroundColor: theme.surface, borderColor: theme.border }]
    ];

    const textStyle = [
        styles.messageText,
        { color: isOwnMessage ? '#FFFFFF' : theme.text }
    ];

    return (
        <View style={[styles.messageContainer, isOwnMessage ? styles.ownMessageContainer : styles.otherMessageContainer]}>
            <TouchableOpacity
                style={bubbleStyle}
                onLongPress={onLongPress}
                activeOpacity={0.8}
            >
                <Text style={textStyle}>{message.content}</Text>
                
                <View style={styles.messageFooter}>
                    <Text style={[
                        styles.timestamp,
                        { color: isOwnMessage ? 'rgba(255,255,255,0.7)' : theme.textSecondary }
                    ]}>
                        {formatTime(message.createdAt)}
                    </Text>
                    
                    {isOwnMessage && (
                        <View style={styles.readStatus}>
                            {message.isRead ? (
                                <Ionicons 
                                    name="checkmark-done" 
                                    size={14} 
                                    color="rgba(255,255,255,0.7)" 
                                />
                            ) : (
                                <Ionicons 
                                    name="checkmark" 
                                    size={14} 
                                    color="rgba(255,255,255,0.7)" 
                                />
                            )}
                        </View>
                    )}
                </View>
            </TouchableOpacity>
            
            {showTimestamp && (
                <Text style={[styles.dateTimestamp, { color: theme.textSecondary }]}>
                    {formatDate(message.createdAt)}
                </Text>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    messageContainer: {
        marginVertical: 2,
        paddingHorizontal: 16,
    },
    ownMessageContainer: {
        alignItems: 'flex-end',
    },
    otherMessageContainer: {
        alignItems: 'flex-start',
    },
    messageBubble: {
        maxWidth: '80%',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 18,
        minWidth: 60,
    },
    ownMessage: {
        borderBottomRightRadius: 4,
    },
    otherMessage: {
        borderWidth: 1,
        borderBottomLeftRadius: 4,
    },
    messageText: {
        fontSize: 16,
        lineHeight: 20,
        marginBottom: 4,
    },
    messageFooter: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        marginTop: 2,
    },
    timestamp: {
        fontSize: 11,
        fontWeight: '400',
    },
    readStatus: {
        marginLeft: 4,
    },
    dateTimestamp: {
        fontSize: 12,
        textAlign: 'center',
        marginTop: 8,
        marginBottom: 4,
        fontWeight: '500',
    },
});

export default MessageBubble;
