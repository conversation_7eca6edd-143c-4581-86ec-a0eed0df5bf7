// jobFinderApp/src/screens/employer/AnalyticsScreen.tsx

import React, { useEffect, useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    ActivityIndicator,
    Dimensions,
    RefreshControl,
    Animated,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import { AppDispatch, RootState } from '../../redux/store';
import { DashboardAnalytics, AnalyticsFilters, ANALYTICS_PERIODS } from '../../types/Analytics';
import DropdownPicker from '../../components/DropdownPicker';
import CustomGradient from '../../components/CustomGradient';
import { fetchCompanyAnalytics, setPeriod } from '../../redux/employer/analyticsSlice';

const { width } = Dimensions.get('window');

const AnalyticsScreen: React.FC = () => {
    const { theme } = useTheme();
    const dispatch = useDispatch<AppDispatch>();
    const [refreshing, setRefreshing] = useState(false);
    const [fadeAnim] = useState(new Animated.Value(0));

    // Get analytics data from Redux store
    const {
        companyAnalytics: analytics,
        companyAnalyticsLoading: isLoading,
        companyAnalyticsError: error,
        currentPeriod: selectedPeriod
    } = useSelector((state: RootState) => state.analytics);

    useEffect(() => {
        loadAnalytics();
        // Fade in animation
        Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
        }).start();
    }, [selectedPeriod]);

    const loadAnalytics = async () => {
        try {
            await dispatch(fetchCompanyAnalytics({ period: selectedPeriod as any })).unwrap();
        } catch (error) {
            console.error('Failed to load analytics:', error);
        }
    };

    const handleRefresh = async () => {
        setRefreshing(true);
        await loadAnalytics();
        setRefreshing(false);
    };

    const handlePeriodChange = (period: string) => {
        dispatch(setPeriod(period as any));
    };

    // Adapter function to handle backend data structure
    const getAnalyticsData = () => {
        // If no analytics data, return mock data for demonstration
        if (!analytics) {
            return {
                overview: {
                    total_jobs: 12,
                    active_jobs: 8,
                    total_applications: 156,
                    total_views: 2340,
                    avg_conversion_rate: 6.7,
                    avg_time_to_fill: 18,
                },
                top_performing_jobs: [
                    {
                        id: '1',
                        title: 'Senior React Developer',
                        applications: 45,
                        views: 320,
                        conversion_rate: 14.1
                    },
                    {
                        id: '2',
                        title: 'Product Manager',
                        applications: 38,
                        views: 280,
                        conversion_rate: 13.6
                    }
                ],
                application_sources: [
                    {
                        source: 'LinkedIn',
                        count: 85,
                        percentage: 54,
                        quality_score: 4.2
                    },
                    {
                        source: 'Indeed',
                        count: 42,
                        percentage: 27,
                        quality_score: 3.8
                    },
                    {
                        source: 'Company Website',
                        count: 29,
                        percentage: 19,
                        quality_score: 4.5
                    }
                ],
                hiring_funnel: [
                    { stage: 'Applied', count: 156, conversion_rate: 100 },
                    { stage: 'Screened', count: 89, conversion_rate: 57 },
                    { stage: 'Interviewed', count: 34, conversion_rate: 22 },
                    { stage: 'Offered', count: 12, conversion_rate: 8 },
                    { stage: 'Hired', count: 8, conversion_rate: 5 }
                ],
                daily_applications: [],
                company_info: {},
                recommendations: [
                    {
                        type: 'optimization',
                        title: 'Improve Job Descriptions',
                        description: 'Add more specific requirements and benefits to increase application quality',
                        impact: 'high',
                        effort: 'low'
                    },
                    {
                        type: 'targeting',
                        title: 'Expand to New Platforms',
                        description: 'Consider posting on GitHub Jobs and AngelList for tech roles',
                        impact: 'medium',
                        effort: 'medium'
                    }
                ]
            };
        }

        // Ensure all required properties exist with defaults
        return {
            overview: {
                total_jobs: analytics.overview?.total_jobs || 0,
                active_jobs: analytics.overview?.active_jobs || 0,
                total_applications: analytics.overview?.total_applications || 0,
                total_views: analytics.overview?.total_views || 0,
                avg_conversion_rate: analytics.overview?.avg_conversion_rate || 0,
                avg_time_to_fill: analytics.overview?.avg_time_to_fill || 0,
            },
            top_performing_jobs: analytics.top_performing_jobs || [],
            application_sources: analytics.application_sources || [],
            hiring_funnel: analytics.hiring_funnel || [],
            daily_applications: analytics.daily_applications || [],
            company_info: analytics.company_info || {},
            recommendations: [
                {
                    type: 'optimization',
                    title: 'Improve Job Descriptions',
                    description: 'Add more specific requirements and benefits to increase application quality',
                    impact: 'high',
                    effort: 'low'
                },
                {
                    type: 'targeting',
                    title: 'Expand to New Platforms',
                    description: 'Consider posting on GitHub Jobs and AngelList for tech roles',
                    impact: 'medium',
                    effort: 'medium'
                }
            ]
        };
    };

    const adaptedAnalytics = getAnalyticsData();

    const renderMetricCard = (title: string, value: string | number, subtitle: string, icon: string, color: string, trend?: { value: number; isPositive: boolean }) => (
        <Animated.View style={[styles.metricCard, { backgroundColor: theme.surface, opacity: fadeAnim }]}>
            <View style={styles.metricGradient}>
                <View style={styles.metricHeader}>
                    <View style={[styles.metricIcon, { backgroundColor: color + '15' }]}>
                        <Ionicons name={icon as any} size={26} color={color} />
                    </View>
                    <View style={styles.metricValueContainer}>
                        <Text style={[styles.metricValue, { color: theme.text }]}>{value}</Text>
                        {trend && (
                            <View style={styles.trendContainer}>
                                <Ionicons
                                    name={trend.isPositive ? 'trending-up' : 'trending-down'}
                                    size={14}
                                    color={trend.isPositive ? theme.success : theme.error}
                                />
                                <Text style={[styles.trendText, {
                                    color: trend.isPositive ? theme.success : theme.error
                                }]}>
                                    {Math.abs(trend.value)}%
                                </Text>
                            </View>
                        )}
                    </View>
                </View>
                <Text style={[styles.metricTitle, { color: theme.text }]}>{title}</Text>
                <Text style={[styles.metricSubtitle, { color: theme.textSecondary }]}>{subtitle}</Text>
            </View>
        </Animated.View>
    );

    const renderTopJobCard = (job: any, index: number) => (
        <TouchableOpacity
            style={[styles.topJobCard, { backgroundColor: theme.surface }]}
            activeOpacity={0.7}
        >
            <View style={styles.topJobCardContent}>
                <View style={[styles.topJobRank, { backgroundColor: theme.primary }]}>
                    <Text style={[styles.rankNumber, { color: 'white' }]}>#{index + 1}</Text>
                </View>
                <View style={styles.topJobInfo}>
                    <Text style={[styles.topJobTitle, { color: theme.text }]} numberOfLines={2}>
                        {job.title}
                    </Text>
                    <View style={styles.topJobStats}>
                        <View style={styles.statItem}>
                            <Ionicons name="eye-outline" size={14} color={theme.textSecondary} />
                            <Text style={[styles.topJobStat, { color: theme.textSecondary }]}>
                                {job.views}
                            </Text>
                        </View>
                        <View style={styles.statItem}>
                            <Ionicons name="people-outline" size={14} color={theme.textSecondary} />
                            <Text style={[styles.topJobStat, { color: theme.textSecondary }]}>
                                {job.applications}
                            </Text>
                        </View>
                        <View style={[styles.conversionBadge, { backgroundColor: theme.success + '15' }]}>
                            <Text style={[styles.conversionRate, { color: theme.success }]}>
                                {job.conversion_rate}%
                            </Text>
                        </View>
                    </View>
                </View>
                <Ionicons name="chevron-forward" size={20} color={theme.textMuted} />
            </View>
        </TouchableOpacity>
    );

    const getSourceIcon = (source: string) => {
        switch (source.toLowerCase()) {
            case 'linkedin': return 'logo-linkedin';
            case 'indeed': return 'briefcase-outline';
            case 'company website': return 'globe-outline';
            case 'referral': return 'people-outline';
            case 'job board': return 'newspaper-outline';
            default: return 'link-outline';
        }
    };

    const renderSourceCard = (source: any) => (
        <View style={[styles.sourceCard, { backgroundColor: theme.surface }]}>
            <View style={styles.sourceHeader}>
                <View style={styles.sourceNameContainer}>
                    <View style={[styles.sourceIcon, { backgroundColor: theme.primary + '15' }]}>
                        <Ionicons
                            name={getSourceIcon(source.source)}
                            size={16}
                            color={theme.primary}
                        />
                    </View>
                    <Text style={[styles.sourceName, { color: theme.text }]}>{source.source}</Text>
                </View>
                <Text style={[styles.sourcePercentage, { color: theme.primary }]}>
                    {source.percentage}%
                </Text>
            </View>
            <View style={styles.sourceStats}>
                <Text style={[styles.sourceCount, { color: theme.textSecondary }]}>
                    {source.count} applications
                </Text>
                <View style={styles.qualityScore}>
                    <Ionicons name="star" size={12} color="#FFD700" />
                    <Text style={[styles.scoreText, { color: theme.textSecondary }]}>
                        {source.quality_score}/100
                    </Text>
                </View>
            </View>
            <View style={[styles.progressBar, { backgroundColor: theme.border }]}>
                <View
                    style={[
                        styles.progressFill,
                        { backgroundColor: theme.primary, width: `${source.percentage}%` }
                    ]}
                />
            </View>
        </View>
    );

    const renderFunnelStage = (stage: any, index: number) => (
        <View style={styles.funnelStage}>
            <View style={[styles.funnelStageCard, { backgroundColor: theme.surface }]}>
                <Text style={[styles.funnelStageName, { color: theme.text }]}>{stage.stage}</Text>
                <Text style={[styles.funnelStageCount, { color: theme.primary }]}>{stage.count}</Text>
                <Text style={[styles.funnelStageRate, { color: theme.textSecondary }]}>
                    {stage.conversion_rate}% conversion
                </Text>
                <Text style={[styles.funnelStageTime, { color: theme.textMuted }]}>
                    {stage.avg_time} days avg
                </Text>
            </View>
            {index < (analytics?.hiring_funnel?.length || 0) - 1 && (
                <View style={styles.funnelArrow}>
                    <Ionicons name="chevron-down" size={16} color={theme.textMuted} />
                </View>
            )}
        </View>
    );

    if (isLoading) {
        return (
            <View style={[styles.loadingContainer, { backgroundColor: theme.background }]}>
                <ActivityIndicator size="large" color={theme.primary} />
                <Text style={[styles.loadingText, { color: theme.text }]}>Loading analytics...</Text>
            </View>
        );
    }

    if (error) {
        return (
            <View style={[styles.errorContainer, { backgroundColor: theme.background }]}>
                <Ionicons name="alert-circle-outline" size={48} color={theme.error} />
                <Text style={[styles.errorTitle, { color: theme.error }]}>Failed to load analytics</Text>
                <Text style={[styles.errorText, { color: theme.textSecondary }]}>{error}</Text>
                <TouchableOpacity
                    style={[styles.retryButton, { backgroundColor: theme.primary }]}
                    onPress={loadAnalytics}
                >
                    <Text style={styles.retryButtonText}>Retry</Text>
                </TouchableOpacity>
            </View>
        );
    }

    // adaptedAnalytics now always returns data (real or mock), so no need for null check

    return (
        <View style={[styles.container, { backgroundColor: theme.background }]}>
            {/* Enhanced Header */}
            <View style={[styles.headerGradient, { backgroundColor: theme.primary }]}>
                <View style={styles.headerContent}>
                    <View style={styles.headerTitleContainer}>
                        <Text style={styles.headerTitle}>Analytics Dashboard</Text>
                        <Text style={styles.headerSubtitle}>Track your hiring performance</Text>
                    </View>
                    <View style={styles.headerActions}>
                        <Text style={[styles.headerSubtitle, { fontSize: 14 }]}>
                            {selectedPeriod || 'Last 30 days'}
                        </Text>
                    </View>
                </View>
            </View>

            <ScrollView
                style={styles.scrollContent}
                testID="analytics-scroll-view"
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={[theme.primary]}
                        tintColor={theme.primary}
                    />
                }
                showsVerticalScrollIndicator={false}
            >

                {/* Overview Metrics */}
                <View style={styles.metricsContainer}>
                    <Text style={[styles.sectionTitle, { color: theme.text }]}>Overview</Text>
                    <View style={styles.metricsRow}>
                        {renderMetricCard(
                            'Total Jobs',
                            adaptedAnalytics.overview.total_jobs,
                            `${adaptedAnalytics.overview.active_jobs} active`,
                            'briefcase-outline',
                            theme.primary,
                            { value: 12, isPositive: true }
                        )}
                        {renderMetricCard(
                            'Applications',
                            adaptedAnalytics.overview.total_applications,
                            'Total received',
                            'people-outline',
                            theme.success,
                            { value: 8, isPositive: true }
                        )}
                    </View>
                    <View style={styles.metricsRow}>
                        {renderMetricCard(
                            'Job Views',
                            adaptedAnalytics.overview.total_views.toLocaleString(),
                            'Total views',
                            'eye-outline',
                            theme.accent,
                            { value: 15, isPositive: true }
                        )}
                        {renderMetricCard(
                            'Conversion Rate',
                            `${adaptedAnalytics.overview.avg_conversion_rate}%`,
                            'Views to applications',
                            'trending-up-outline',
                            theme.warning,
                            { value: 3, isPositive: false }
                        )}
                    </View>
                </View>

            {/* Top Performing Jobs */}
            <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: theme.text }]}>Top Performing Jobs</Text>
                {(analytics?.top_performing_jobs || []).map((job, index) => (
                    <View key={job.id || `job-${index}`}>
                        {renderTopJobCard(job, index)}
                    </View>
                ))}
            </View>

            {/* Application Sources */}
            <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: theme.text }]}>Application Sources</Text>
                {(analytics?.application_sources || []).map((source, index) => (
                    <View key={source.source || `source-${index}`}>
                        {renderSourceCard(source)}
                    </View>
                ))}
            </View>

            {/* Hiring Funnel */}
            <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: theme.text }]}>Hiring Funnel</Text>
                <View style={styles.funnelContainer}>
                    {(analytics?.hiring_funnel || []).map((stage, index) => (
                        <View key={stage.stage || `funnel-${index}`}>
                            {renderFunnelStage(stage, index)}
                        </View>
                    ))}
                </View>
            </View>

            {/* Recommendations */}
            <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: theme.text }]}>Recommendations</Text>
                {(analytics?.recommendations || []).map((rec, index) => (
                    <View key={index} style={[styles.recommendationCard, { backgroundColor: theme.surface }]}>
                        <View style={styles.recommendationHeader}>
                            <Text style={[styles.recommendationTitle, { color: theme.text }]}>
                                {rec.title}
                            </Text>
                            <View style={styles.recommendationBadges}>
                                <View style={[
                                    styles.impactBadge,
                                    { backgroundColor: rec.impact === 'high' ? theme.success :
                                                     rec.impact === 'medium' ? theme.warning : theme.textMuted }
                                ]}>
                                    <Text style={styles.badgeText}>{rec.impact.toUpperCase()}</Text>
                                </View>
                            </View>
                        </View>
                        <Text style={[styles.recommendationDescription, { color: theme.textSecondary }]}>
                            {rec.description}
                        </Text>
                    </View>
                ))}
            </View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 10,
        fontSize: 16,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 40,
    },
    errorTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginTop: 20,
        marginBottom: 10,
    },
    errorText: {
        fontSize: 16,
        textAlign: 'center',
    },
    // Enhanced Header Styles
    headerGradient: {
        paddingTop: 60,
        paddingBottom: 20,
        paddingHorizontal: 20,
        height: 140,
    },
    headerContent: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
    },
    headerTitleContainer: {
        flex: 1,
    },
    headerTitle: {
        fontSize: 28,
        fontWeight: 'bold',
        color: 'white',
        marginBottom: 4,
    },
    headerSubtitle: {
        fontSize: 16,
        color: 'rgba(255, 255, 255, 0.8)',
    },
    headerActions: {
        marginLeft: 16,
    },
    scrollContent: {
        flex: 1,
    },
    // Enhanced Metrics Styles
    metricsContainer: {
        paddingHorizontal: 20,
        marginBottom: 24,
        marginTop: 20,
    },
    metricsRow: {
        flexDirection: 'row',
        marginBottom: 16,
        gap: 16,
    },
    metricCard: {
        flex: 1,
        borderRadius: 16,
        elevation: 4,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.12,
        shadowRadius: 8,
        overflow: 'hidden',
    },
    metricGradient: {
        padding: 20,
    },
    metricHeader: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    metricIcon: {
        width: 48,
        height: 48,
        borderRadius: 24,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    metricValueContainer: {
        flex: 1,
    },
    metricValue: {
        fontSize: 28,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    trendContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    trendText: {
        fontSize: 12,
        fontWeight: '600',
    },
    metricTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 4,
    },
    metricSubtitle: {
        fontSize: 14,
        opacity: 0.8,
    },
    section: {
        paddingHorizontal: 20,
        marginBottom: 28,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 20,
    },
    // Enhanced Top Job Card Styles
    topJobCard: {
        borderRadius: 16,
        marginBottom: 12,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.1,
        shadowRadius: 6,
        overflow: 'hidden',
    },
    topJobCardContent: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 20,
    },
    topJobRank: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    rankNumber: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    topJobInfo: {
        flex: 1,
    },
    topJobTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 8,
        lineHeight: 24,
    },
    topJobStats: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 16,
    },
    statItem: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    topJobStat: {
        fontSize: 14,
        fontWeight: '500',
    },
    conversionBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    conversionRate: {
        fontSize: 12,
        fontWeight: '600',
    },
    // Enhanced Source Card Styles
    sourceCard: {
        padding: 20,
        borderRadius: 16,
        marginBottom: 12,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.1,
        shadowRadius: 6,
    },
    sourceHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    sourceNameContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    sourceIcon: {
        width: 32,
        height: 32,
        borderRadius: 16,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    sourceName: {
        fontSize: 16,
        fontWeight: '600',
    },
    sourcePercentage: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    sourceStats: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    sourceCount: {
        fontSize: 14,
        fontWeight: '500',
    },
    qualityScore: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    scoreText: {
        fontSize: 12,
        fontWeight: '500',
    },
    progressBar: {
        height: 6,
        borderRadius: 3,
        overflow: 'hidden',
    },
    progressFill: {
        height: '100%',
        borderRadius: 3,
    },
    funnelContainer: {
        alignItems: 'center',
    },
    funnelStage: {
        alignItems: 'center',
        marginBottom: 8,
    },
    funnelStageCard: {
        width: width * 0.7,
        padding: 16,
        borderRadius: 12,
        alignItems: 'center',
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    funnelStageName: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    funnelStageCount: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 2,
    },
    funnelStageRate: {
        fontSize: 12,
        marginBottom: 2,
    },
    funnelStageTime: {
        fontSize: 10,
    },
    funnelArrow: {
        marginVertical: 4,
    },
    recommendationCard: {
        padding: 16,
        borderRadius: 12,
        marginBottom: 12,
        elevation: 1,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
    },
    recommendationHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 8,
    },
    recommendationTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        flex: 1,
        marginRight: 12,
    },
    recommendationBadges: {
        flexDirection: 'row',
        gap: 4,
    },
    impactBadge: {
        paddingHorizontal: 6,
        paddingVertical: 2,
        borderRadius: 10,
    },
    badgeText: {
        color: 'white',
        fontSize: 10,
        fontWeight: 'bold',
    },
    recommendationDescription: {
        fontSize: 14,
        lineHeight: 20,
    },
    retryButton: {
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderRadius: 8,
        marginTop: 16,
    },
    retryButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
});

export default AnalyticsScreen;
