// jobFinderApp/src/screens/chat/ChatDetailScreen.tsx

import React, { useEffect, useState, useRef, useCallback } from 'react';
import {
    View,
    Text,
    StyleSheet,
    FlatList,
    KeyboardAvoidingView,
    Platform,
    ActivityIndicator,
    Alert,
} from 'react-native';
import { useNavigation, useRoute, RouteProp, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useDispatch, useSelector } from 'react-redux';
import { useTheme } from '../../contexts/ThemeContext';
import { AppDispatch, RootState } from '../../redux/store';
import { 
    fetchConversationMessages, 
    markMessagesAsReadREST,
    clearConversationMessages 
} from '../../redux/chat/chatSlice';
import { Message } from '../../redux/chat/chatSlice';
import MessageBubble from '../../components/chat/MessageBubble';
import ChatInput from '../../components/chat/ChatInput';
import socketService from '../../services/socketService';
import { AppStackParamList } from '../../types/navigation';

type ChatDetailNavigationProp = NativeStackNavigationProp<AppStackParamList, 'ChatDetail'>;
type ChatDetailRouteProp = RouteProp<AppStackParamList, 'ChatDetail'>;

const ChatDetailScreen: React.FC = () => {
    const { theme } = useTheme();
    const navigation = useNavigation<ChatDetailNavigationProp>();
    const route = useRoute<ChatDetailRouteProp>();
    const dispatch = useDispatch<AppDispatch>();
    
    const { conversationId } = route.params;
    const { 
        messages, 
        currentConversation, 
        isLoadingMessages, 
        error,
        typingUsers,
        pagination 
    } = useSelector((state: RootState) => state.chat);
    const { user } = useSelector((state: RootState) => state.auth);
    
    const [isConnected, setIsConnected] = useState(false);
    const flatListRef = useRef<FlatList>(null);
    const conversationMessages = messages[conversationId] || [];
    const conversationPagination = pagination[conversationId];

    // Initialize socket connection and join conversation
    useEffect(() => {
        const initializeChat = async () => {
            try {
                // Connect socket if not connected
                if (!socketService.isSocketConnected() && user?.token) {
                    socketService.connect(user.token);
                }
                
                // Join conversation room
                socketService.joinConversation(conversationId);
                setIsConnected(true);
                
                // Load initial messages
                await dispatch(fetchConversationMessages({ conversationId, page: 1 }));
                
            } catch (error) {
                console.error('Error initializing chat:', error);
                Alert.alert('Error', 'Failed to initialize chat');
            }
        };

        initializeChat();

        return () => {
            // Leave conversation room on cleanup
            socketService.leaveConversation(conversationId);
        };
    }, [conversationId, dispatch, user?.token]);

    // Set navigation title
    useEffect(() => {
        if (currentConversation && user) {
            const otherUser = user.role === 'jobseeker' 
                ? currentConversation.employer 
                : currentConversation.jobSeeker;
            
            navigation.setOptions({
                title: `${otherUser.firstName} ${otherUser.lastName}`,
                headerTitleStyle: { color: theme.text },
                headerStyle: { backgroundColor: theme.headerBackground },
            });
        }
    }, [currentConversation, user, navigation, theme]);

    // Mark messages as read when screen is focused
    useFocusEffect(
        useCallback(() => {
            if (conversationMessages.length > 0) {
                const unreadMessages = conversationMessages.filter(
                    msg => !msg.isRead && msg.receiver === user?._id
                );
                
                if (unreadMessages.length > 0) {
                    // Mark as read via socket
                    socketService.markAsRead(conversationId);
                    
                    // Also mark via REST as backup
                    dispatch(markMessagesAsReadREST({ conversationId }));
                }
            }
        }, [conversationMessages, conversationId, user?._id, dispatch])
    );

    // Auto-scroll to bottom when new messages arrive
    useEffect(() => {
        if (conversationMessages.length > 0) {
            setTimeout(() => {
                flatListRef.current?.scrollToEnd({ animated: true });
            }, 100);
        }
    }, [conversationMessages.length]);

    const handleSendMessage = (content: string) => {
        if (isConnected && content.trim()) {
            socketService.sendMessage(conversationId, content.trim());
        } else {
            Alert.alert('Error', 'Unable to send message. Please check your connection.');
        }
    };

    const handleTypingStart = () => {
        if (isConnected) {
            socketService.startTyping(conversationId);
        }
    };

    const handleTypingStop = () => {
        if (isConnected) {
            socketService.stopTyping(conversationId);
        }
    };

    const loadMoreMessages = async () => {
        if (conversationPagination?.hasNext && !isLoadingMessages) {
            const nextPage = (conversationPagination.currentPage || 1) + 1;
            await dispatch(fetchConversationMessages({ 
                conversationId, 
                page: nextPage 
            }));
        }
    };

    const renderMessage = ({ item, index }: { item: Message; index: number }) => {
        const isOwnMessage = item.sender === user?._id;
        const previousMessage = index > 0 ? conversationMessages[index - 1] : null;
        const showTimestamp = !previousMessage || 
            new Date(item.createdAt).toDateString() !== new Date(previousMessage.createdAt).toDateString();

        return (
            <MessageBubble
                message={item}
                isOwnMessage={isOwnMessage}
                showTimestamp={showTimestamp}
            />
        );
    };

    const renderTypingIndicator = () => {
        const typingUserIds = typingUsers[conversationId] || [];
        const otherUserTyping = typingUserIds.filter(userId => userId !== user?._id);
        
        if (otherUserTyping.length === 0) return null;

        return (
            <View style={[styles.typingIndicator, { backgroundColor: theme.surface }]}>
                <View style={styles.typingDots}>
                    <View style={[styles.dot, { backgroundColor: theme.textSecondary }]} />
                    <View style={[styles.dot, { backgroundColor: theme.textSecondary }]} />
                    <View style={[styles.dot, { backgroundColor: theme.textSecondary }]} />
                </View>
                <Text style={[styles.typingText, { color: theme.textSecondary }]}>
                    typing...
                </Text>
            </View>
        );
    };

    const renderHeader = () => {
        if (!conversationPagination?.hasNext) return null;
        
        return (
            <View style={styles.loadMoreContainer}>
                {isLoadingMessages ? (
                    <ActivityIndicator size="small" color={theme.primary} />
                ) : (
                    <Text 
                        style={[styles.loadMoreText, { color: theme.primary }]}
                        onPress={loadMoreMessages}
                    >
                        Load more messages
                    </Text>
                )}
            </View>
        );
    };

    if (isLoadingMessages && conversationMessages.length === 0) {
        return (
            <View style={[styles.container, styles.centered, { backgroundColor: theme.background }]}>
                <ActivityIndicator size="large" color={theme.primary} />
                <Text style={[styles.loadingText, { color: theme.text }]}>Loading messages...</Text>
            </View>
        );
    }

    return (
        <KeyboardAvoidingView 
            style={[styles.container, { backgroundColor: theme.background }]}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
        >
            <FlatList
                ref={flatListRef}
                data={conversationMessages}
                renderItem={renderMessage}
                keyExtractor={(item) => item._id}
                ListHeaderComponent={renderHeader}
                ListFooterComponent={renderTypingIndicator}
                contentContainerStyle={styles.messagesList}
                showsVerticalScrollIndicator={false}
                onEndReached={loadMoreMessages}
                onEndReachedThreshold={0.1}
                inverted={false}
            />
            
            <ChatInput
                onSendMessage={handleSendMessage}
                onTypingStart={handleTypingStart}
                onTypingStop={handleTypingStop}
                disabled={!isConnected}
                placeholder={isConnected ? 'Type a message...' : 'Connecting...'}
            />
        </KeyboardAvoidingView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    centered: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
    },
    messagesList: {
        paddingVertical: 8,
    },
    loadMoreContainer: {
        padding: 16,
        alignItems: 'center',
    },
    loadMoreText: {
        fontSize: 14,
        fontWeight: '500',
    },
    typingIndicator: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 8,
        marginHorizontal: 16,
        marginBottom: 8,
        borderRadius: 18,
        alignSelf: 'flex-start',
    },
    typingDots: {
        flexDirection: 'row',
        marginRight: 8,
    },
    dot: {
        width: 4,
        height: 4,
        borderRadius: 2,
        marginHorizontal: 1,
    },
    typingText: {
        fontSize: 12,
        fontStyle: 'italic',
    },
});

export default ChatDetailScreen;
