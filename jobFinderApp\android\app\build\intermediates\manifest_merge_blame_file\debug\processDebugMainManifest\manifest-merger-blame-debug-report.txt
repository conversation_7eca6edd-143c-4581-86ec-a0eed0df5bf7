1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.jobfinderapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:4:5-67
11-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
12-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:5:5-79
12-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:5:22-76
13    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
13-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:6:5-81
13-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:6:22-78
14    <uses-permission android:name="android.permission.CAMERA" />
14-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:7:5-65
14-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:7:22-62
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:8:5-80
15-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:8:22-77
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:9:5-81
16-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:9:22-78
17    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
17-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:10:5-76
17-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:10:22-73
18
19    <uses-feature
19-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:12:5-85
20        android:name="android.hardware.camera"
20-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:12:19-57
21        android:required="false" />
21-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:12:58-82
22    <uses-feature
22-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:13:5-95
23        android:name="android.hardware.camera.autofocus"
23-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:13:19-67
24        android:required="false" />
24-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:13:68-92
25
26    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
27    <!--
28    This manifest file is used only by Gradle to configure debug-only capabilities
29    for React Native Apps.
30    -->
31    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
31-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d5096a21f09ffba54b9358be8babf3\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
31-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d5096a21f09ffba54b9358be8babf3\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:16:22-75
32
33    <permission
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
34        android:name="com.jobfinderapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
35        android:protectionLevel="signature" />
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
36
37    <uses-permission android:name="com.jobfinderapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
38
39    <application
39-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:15:5-63:19
40        android:name="com.jobfinderapp.MainApplication"
40-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:16:9-40
41        android:allowBackup="false"
41-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:20:9-36
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53fd33931d11466b8971a3a1b9d808f4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:hardwareAccelerated="true"
45-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:25:9-43
46        android:icon="@mipmap/ic_launcher"
46-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:18:9-43
47        android:label="@string/app_name"
47-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:17:9-41
48        android:largeHeap="true"
48-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:26:9-33
49        android:networkSecurityConfig="@xml/network_security_config"
49-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:24:9-69
50        android:requestLegacyExternalStorage="false"
50-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:27:9-53
51        android:roundIcon="@mipmap/ic_launcher_round"
51-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:19:9-54
52        android:supportsRtl="true"
52-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:22:9-35
53        android:theme="@style/AppTheme"
53-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:21:9-40
54        android:usesCleartextTraffic="true" >
54-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:23:9-45
55        <activity
55-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:30:9-51:20
56            android:name="com.jobfinderapp.MainActivity"
56-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:31:13-41
57            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
57-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:33:13-122
58            android:exported="true"
58-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:36:13-36
59            android:label="@string/app_name"
59-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:32:13-45
60            android:launchMode="singleTask"
60-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:34:13-44
61            android:windowSoftInputMode="adjustResize" >
61-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:35:13-55
62            <intent-filter>
62-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:38:13-41:29
63                <action android:name="android.intent.action.MAIN" />
63-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:39:17-69
63-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:39:25-66
64
65                <category android:name="android.intent.category.LAUNCHER" />
65-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:40:17-77
65-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:40:27-74
66            </intent-filter>
67            <intent-filter>
67-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:43:13-49:29
68                <action android:name="android.intent.action.VIEW" />
68-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:44:17-69
68-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:44:25-66
69
70                <category android:name="android.intent.category.DEFAULT" />
70-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:45:17-76
70-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:45:27-73
71                <category android:name="android.intent.category.BROWSABLE" />
71-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:46:17-78
71-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:46:27-75
72
73                <data
73-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:47:17-74
74                    android:host="app"
74-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:47:53-71
75                    android:scheme="jobfinderapp" />
75-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:47:23-52
76                <data android:scheme="jobfinderapp" />
76-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:47:17-74
76-->E:\job finder\jobFinderApp\android\app\src\main\AndroidManifest.xml:47:23-52
77            </intent-filter>
78        </activity>
79
80        <provider
81            android:name="androidx.core.content.FileProvider"
82            android:authorities="com.jobfinderapp.fileprovider"
83            android:exported="false"
84            android:grantUriPermissions="true" >
85            <meta-data
85-->[:react-native-image-picker] E:\job finder\jobFinderApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:70
86                android:name="android.support.FILE_PROVIDER_PATHS"
86-->[:react-native-image-picker] E:\job finder\jobFinderApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
87                android:resource="@xml/file_paths" />
87-->[:react-native-image-picker] E:\job finder\jobFinderApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
88        </provider>
89        <provider
89-->[:react-native-image-picker] E:\job finder\jobFinderApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
90            android:name="com.imagepicker.ImagePickerProvider"
90-->[:react-native-image-picker] E:\job finder\jobFinderApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-63
91            android:authorities="com.jobfinderapp.imagepickerprovider"
91-->[:react-native-image-picker] E:\job finder\jobFinderApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-71
92            android:exported="false"
92-->[:react-native-image-picker] E:\job finder\jobFinderApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
93            android:grantUriPermissions="true" >
93-->[:react-native-image-picker] E:\job finder\jobFinderApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
94            <meta-data
94-->[:react-native-image-picker] E:\job finder\jobFinderApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:70
95                android:name="android.support.FILE_PROVIDER_PATHS"
95-->[:react-native-image-picker] E:\job finder\jobFinderApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
96                android:resource="@xml/imagepicker_provider_paths" />
96-->[:react-native-image-picker] E:\job finder\jobFinderApp\node_modules\react-native-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
97        </provider>
98
99        <activity
99-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d5096a21f09ffba54b9358be8babf3\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
100            android:name="com.facebook.react.devsupport.DevSettingsActivity"
100-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d5096a21f09ffba54b9358be8babf3\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
101            android:exported="false" />
101-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f3d5096a21f09ffba54b9358be8babf3\transformed\jetified-react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
102        <activity
102-->[com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bacb697df03e59b48bfa93da5f8e54b4\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:23:9-26:75
103            android:name="com.google.android.gms.common.api.GoogleApiActivity"
103-->[com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bacb697df03e59b48bfa93da5f8e54b4\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:24:13-79
104            android:exported="false"
104-->[com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bacb697df03e59b48bfa93da5f8e54b4\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:25:13-37
105            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
105-->[com.google.android.gms:play-services-base:17.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bacb697df03e59b48bfa93da5f8e54b4\transformed\jetified-play-services-base-17.5.0\AndroidManifest.xml:26:13-72
106
107        <meta-data
107-->[com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b5921f6d5767baf9692e156fc151a26\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:23:9-25:69
108            android:name="com.google.android.gms.version"
108-->[com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b5921f6d5767baf9692e156fc151a26\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:24:13-58
109            android:value="@integer/google_play_services_version" />
109-->[com.google.android.gms:play-services-basement:17.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b5921f6d5767baf9692e156fc151a26\transformed\jetified-play-services-basement-17.5.0\AndroidManifest.xml:25:13-66
110
111        <provider
111-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
112            android:name="androidx.startup.InitializationProvider"
112-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
113            android:authorities="com.jobfinderapp.androidx-startup"
113-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
114            android:exported="false" >
114-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
115            <meta-data
115-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
116                android:name="androidx.emoji2.text.EmojiCompatInitializer"
116-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
117                android:value="androidx.startup" />
117-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8107131467faefaa27f5d95d9772abe\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
118            <meta-data
118-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2654b6e7cbf61e5f358fda9251e10cad\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
119                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
119-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2654b6e7cbf61e5f358fda9251e10cad\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
120                android:value="androidx.startup" />
120-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\2654b6e7cbf61e5f358fda9251e10cad\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
121            <meta-data
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
122                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
123                android:value="androidx.startup" />
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
124        </provider>
125
126        <receiver
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
127            android:name="androidx.profileinstaller.ProfileInstallReceiver"
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
128            android:directBootAware="false"
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
129            android:enabled="true"
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
130            android:exported="true"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
131            android:permission="android.permission.DUMP" >
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
132            <intent-filter>
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
133                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
134            </intent-filter>
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
136                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
139                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
142                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b5b5309fc7c6ec1e1e60fa2e2fbd5b4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
143            </intent-filter>
144        </receiver>
145
146        <meta-data
146-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\966b41a3ee5c43356ab9f94544949b85\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:12:9-14:37
147            android:name="com.facebook.soloader.enabled"
147-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\966b41a3ee5c43356ab9f94544949b85\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:13:13-57
148            android:value="false" />
148-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\966b41a3ee5c43356ab9f94544949b85\transformed\jetified-soloader-0.12.1\AndroidManifest.xml:14:13-34
149    </application>
150
151</manifest>
