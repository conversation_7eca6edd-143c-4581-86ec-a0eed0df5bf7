// jobFinderApp/src/components/AnalyticsDemo.tsx
// Demo component showcasing the enhanced analytics features

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface AnalyticsDemoProps {
    title: string;
    description: string;
}

const AnalyticsDemo: React.FC<AnalyticsDemoProps> = ({ title, description }) => {
    const { theme } = useTheme();

    return (
        <View style={[styles.container, { backgroundColor: theme.surface }]}>
            <View style={styles.header}>
                <Ionicons name="analytics-outline" size={24} color={theme.primary} />
                <Text style={[styles.title, { color: theme.text }]}>{title}</Text>
            </View>
            <Text style={[styles.description, { color: theme.textSecondary }]}>
                {description}
            </Text>
            
            <View style={styles.features}>
                <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={theme.success} />
                    <Text style={[styles.featureText, { color: theme.text }]}>
                        Enhanced visual design with gradients
                    </Text>
                </View>
                <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={theme.success} />
                    <Text style={[styles.featureText, { color: theme.text }]}>
                        Trend indicators with percentage changes
                    </Text>
                </View>
                <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={theme.success} />
                    <Text style={[styles.featureText, { color: theme.text }]}>
                        Interactive cards with better spacing
                    </Text>
                </View>
                <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={theme.success} />
                    <Text style={[styles.featureText, { color: theme.text }]}>
                        Pull-to-refresh functionality
                    </Text>
                </View>
                <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={theme.success} />
                    <Text style={[styles.featureText, { color: theme.text }]}>
                        Smooth animations and transitions
                    </Text>
                </View>
                <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={theme.success} />
                    <Text style={[styles.featureText, { color: theme.text }]}>
                        Fixed React key prop warnings
                    </Text>
                </View>
                <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={16} color={theme.success} />
                    <Text style={[styles.featureText, { color: theme.text }]}>
                        Custom gradient without native dependencies
                    </Text>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: 20,
        borderRadius: 16,
        margin: 16,
        elevation: 3,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 3 },
        shadowOpacity: 0.1,
        shadowRadius: 6,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        marginLeft: 12,
    },
    description: {
        fontSize: 14,
        lineHeight: 20,
        marginBottom: 16,
    },
    features: {
        gap: 8,
    },
    feature: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    featureText: {
        fontSize: 14,
        flex: 1,
    },
});

export default AnalyticsDemo;
