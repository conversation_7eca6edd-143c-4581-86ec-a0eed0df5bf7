// store.ts

import { configureStore } from '@reduxjs/toolkit';
import authReducer from './auth/authSlice';
import jobsReducer from './jobs/jobSlice';
import profileReducer from './profile/profileSlice'; // Make sure we're using the correct one
import applicationsReducer from './applications';
import companyReducer from './employer/companySlice';
import employerJobsReducer from './employer/employerJobsSlice';
import employerApplicationsReducer from './employer/employerApplicationsSlice';
import settingsReducer from './employer/settingsSlice';
import analyticsReducer from './employer/analyticsSlice';
import publicCompanyReducer from './company/companySlice';
import templatesReducer from './templates/templatesSlice';
import emailAutomationReducer from './email/emailAutomationSlice';
import chatReducer from './chat/chatSlice';

export const store = configureStore({
    reducer: {
        auth: authReducer,
        jobs: jobsReducer,
        profile: profileReducer,
        applications: applicationsReducer,
        company: companyReducer,
        employerJobs: employerJobsReducer,
        employerApplications: employerApplicationsReducer,
        settings: settingsReducer,
        analytics: analyticsReducer,
        publicCompany: publicCompanyReducer,
        templates: templatesReducer,
        emailAutomation: emailAutomationReducer,
        chat: chatReducer,
    },
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            serializableCheck: {
                // Increase the warning threshold to reduce false positives
                warnAfter: 128,
                // Ignore these action types that might contain non-serializable values
                ignoredActions: [
                    'persist/PERSIST',
                    'persist/REHYDRATE',
                    'persist/PAUSE',
                    'persist/PURGE',
                    'persist/REGISTER',
                    'persist/FLUSH',
                    // Add any other actions that might contain non-serializable data
                ],
                // Ignore these field paths in all actions
                ignoredActionPaths: [
                    'meta.arg',
                    'payload.timestamp',
                    'payload.config',
                    'payload.request',
                    'error.config',
                    'error.request',
                    'error.response',
                ],
                // Ignore these paths in the state
                ignoredPaths: [
                    'auth.user.lastLogin',
                    'jobs.lastFetch',
                    'applications.lastFetch',
                ],
            },
            immutableCheck: {
                // Increase the warning threshold for immutability checks
                warnAfter: 128,
            },
        }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;