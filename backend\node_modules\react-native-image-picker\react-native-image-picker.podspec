require 'json'

package = JSON.parse(File.read(File.join(__dir__, 'package.json')))

Pod::Spec.new do |s|
  s.name         = package['name']
  s.version      = package['version']
  s.summary      = package['description']
  s.license      = package['license']

  s.authors      = package['author']
  s.homepage     = package['homepage']
  s.platform     = :ios, "9.0"

  s.source       = { :git => "https://github.com/react-native-image-picker/react-native-image-picker.git", :tag => "v#{s.version}" }
  s.source_files  = "ios/*.{h,m,mm}"

  s.resource_bundles = {
    'RNImagePickerPrivacyInfo' => ['ios/PrivacyInfo.xcprivacy'],
  }

  s.frameworks   = 'Photos','PhotosUI', 'AVFoundation', 'CoreMedia'

  if defined?(install_modules_dependencies) != nil
    install_modules_dependencies(s)
  else

    if ENV['RCT_NEW_ARCH_ENABLED'] == '1'
      folly_compiler_flags = '-DFOLLY_NO_CONFIG -DFOLLY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -Wno-comma -Wno-shorten-64-to-32'

      s.pod_target_xcconfig = {
        'HEADER_SEARCH_PATHS' => '"$(PODS_ROOT)/boost" "$(PODS_ROOT)/boost-for-react-native" "$(PODS_ROOT)/RCT-Folly"',
        'CLANG_CXX_LANGUAGE_STANDARD' => 'c++17'
      }

      s.compiler_flags  = folly_compiler_flags + ' -DRN_FABRIC_ENABLED -fmodules -fcxx-modules'

      s.dependency "React"
      s.dependency "React-RCTFabric" # This is for fabric component
      s.dependency "React-Codegen"
      s.dependency "RCT-Folly"
      s.dependency "RCTRequired"
      s.dependency "RCTTypeSafety"
      s.dependency "ReactCommon/turbomodule/core"
    else
      s.dependency "React-Core"
    end
  end
end
