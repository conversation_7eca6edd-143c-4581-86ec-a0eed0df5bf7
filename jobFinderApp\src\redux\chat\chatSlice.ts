// jobFinderApp/src/redux/chat/chatSlice.ts

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';

// Types
export interface User {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
    role: 'jobseeker' | 'employer';
    profileImage?: string;
}

export interface Job {
    _id: string;
    title: string;
    company: string;
}

export interface Application {
    _id: string;
    status: string;
    createdAt: string;
}

export interface Message {
    _id: string;
    conversation: string;
    sender: string;
    receiver: string;
    content: string;
    messageType: 'text' | 'image' | 'file';
    isRead: boolean;
    createdAt: string;
    updatedAt: string;
    senderDetails?: User;
    receiverDetails?: User;
}

export interface Conversation {
    _id: string;
    jobSeeker: User;
    employer: User;
    job: Job;
    application: Application;
    lastMessage?: {
        content: string;
        sender: string;
        timestamp: string;
    };
    unreadCount: {
        jobSeeker: number;
        employer: number;
    };
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
}

export interface ChatState {
    conversations: Conversation[];
    currentConversation: Conversation | null;
    messages: { [conversationId: string]: Message[] };
    isLoading: boolean;
    isLoadingMessages: boolean;
    error: string | null;
    totalUnreadCount: number;
    typingUsers: { [conversationId: string]: string[] };
    onlineUsers: string[]; // Changed from Set to array for serialization
    pagination: {
        [conversationId: string]: {
            currentPage: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    };
}

const initialState: ChatState = {
    conversations: [],
    currentConversation: null,
    messages: {},
    isLoading: false,
    isLoadingMessages: false,
    error: null,
    totalUnreadCount: 0,
    typingUsers: {},
    onlineUsers: [], // Changed from Set to array
    pagination: {}
};

// Async Thunks
export const startConversation = createAsyncThunk(
    'chat/startConversation',
    async (applicationId: string, { rejectWithValue }) => {
        try {
            const response = await api.post('/chat/conversations/start', { applicationId });
            return response.data.conversation;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.msg || 'Failed to start conversation');
        }
    }
);

export const fetchConversations = createAsyncThunk(
    'chat/fetchConversations',
    async ({ page = 1, limit = 20 }: { page?: number; limit?: number } = {}, { rejectWithValue }) => {
        try {
            const response = await api.get(`/chat/conversations?page=${page}&limit=${limit}`);
            return response.data;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.msg || 'Failed to fetch conversations');
        }
    }
);

export const fetchConversationMessages = createAsyncThunk(
    'chat/fetchConversationMessages',
    async (
        { conversationId, page = 1, limit = 50 }: { conversationId: string; page?: number; limit?: number },
        { rejectWithValue }
    ) => {
        try {
            const response = await api.get(`/chat/conversations/${conversationId}/messages?page=${page}&limit=${limit}`);
            return {
                conversationId,
                ...response.data
            };
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.msg || 'Failed to fetch messages');
        }
    }
);

export const sendMessageREST = createAsyncThunk(
    'chat/sendMessage',
    async (
        { conversationId, content, messageType = 'text' }: { 
            conversationId: string; 
            content: string; 
            messageType?: 'text' | 'image' | 'file' 
        },
        { rejectWithValue }
    ) => {
        try {
            const response = await api.post('/chat/messages', {
                conversationId,
                content,
                messageType
            });
            return {
                conversationId,
                message: response.data.message
            };
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.msg || 'Failed to send message');
        }
    }
);

export const markMessagesAsReadREST = createAsyncThunk(
    'chat/markMessagesAsRead',
    async (
        { conversationId, messageIds }: { conversationId: string; messageIds?: string[] },
        { rejectWithValue }
    ) => {
        try {
            await api.patch(`/chat/conversations/${conversationId}/read`, { messageIds });
            return { conversationId, messageIds };
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.msg || 'Failed to mark messages as read');
        }
    }
);

export const fetchUnreadCount = createAsyncThunk(
    'chat/fetchUnreadCount',
    async (_, { rejectWithValue }) => {
        try {
            const response = await api.get('/chat/unread-count');
            return response.data.totalUnread;
        } catch (error: any) {
            return rejectWithValue(error.response?.data?.msg || 'Failed to fetch unread count');
        }
    }
);

// Chat Slice
const chatSlice = createSlice({
    name: 'chat',
    initialState,
    reducers: {
        // Real-time message handling
        addMessage: (state, action: PayloadAction<{ conversationId: string; message: Message }>) => {
            const { conversationId, message } = action.payload;
            
            if (!state.messages[conversationId]) {
                state.messages[conversationId] = [];
            }
            
            // Avoid duplicates
            const exists = state.messages[conversationId].some(m => m._id === message._id);
            if (!exists) {
                state.messages[conversationId].push(message);
                
                // Update conversation's last message
                const conversation = state.conversations.find(c => c._id === conversationId);
                if (conversation) {
                    conversation.lastMessage = {
                        content: message.content,
                        sender: message.sender,
                        timestamp: message.createdAt
                    };
                    conversation.updatedAt = message.createdAt;
                }
            }
        },

        // Mark messages as read
        markMessagesAsRead: (state, action: PayloadAction<{ conversationId: string; messageIds: string[] }>) => {
            const { conversationId, messageIds } = action.payload;
            
            if (state.messages[conversationId]) {
                state.messages[conversationId].forEach(message => {
                    if (messageIds.length === 0 || messageIds.includes(message._id)) {
                        message.isRead = true;
                    }
                });
            }
            
            // Reset unread count for conversation
            const conversation = state.conversations.find(c => c._id === conversationId);
            if (conversation) {
                // Reset the appropriate unread count based on current user role
                // This will be handled by the component based on user role
                conversation.unreadCount.jobSeeker = 0;
                conversation.unreadCount.employer = 0;
            }
        },

        // Update typing status
        updateTypingStatus: (state, action: PayloadAction<{ 
            conversationId: string; 
            userId: string; 
            isTyping: boolean 
        }>) => {
            const { conversationId, userId, isTyping } = action.payload;
            
            if (!state.typingUsers[conversationId]) {
                state.typingUsers[conversationId] = [];
            }
            
            const typingList = state.typingUsers[conversationId];
            const userIndex = typingList.indexOf(userId);
            
            if (isTyping && userIndex === -1) {
                typingList.push(userId);
            } else if (!isTyping && userIndex > -1) {
                typingList.splice(userIndex, 1);
            }
        },

        // Update user online status
        updateUserOnlineStatus: (state, action: PayloadAction<{ userId: string; isOnline: boolean }>) => {
            const { userId, isOnline } = action.payload;

            if (isOnline) {
                // Add user if not already in the array
                if (!state.onlineUsers.includes(userId)) {
                    state.onlineUsers.push(userId);
                }
            } else {
                // Remove user from array
                state.onlineUsers = state.onlineUsers.filter(id => id !== userId);
            }
        },

        // Increment unread count
        incrementUnreadCount: (state, action: PayloadAction<string>) => {
            const conversationId = action.payload;
            const conversation = state.conversations.find(c => c._id === conversationId);
            
            if (conversation) {
                // This will be properly handled based on user role in the component
                state.totalUnreadCount += 1;
            }
        },

        // Set current conversation
        setCurrentConversation: (state, action: PayloadAction<Conversation | null>) => {
            state.currentConversation = action.payload;
        },

        // Clear error
        clearError: (state) => {
            state.error = null;
        },

        // Clear messages for a conversation
        clearConversationMessages: (state, action: PayloadAction<string>) => {
            const conversationId = action.payload;
            delete state.messages[conversationId];
            delete state.pagination[conversationId];
        }
    },
    extraReducers: (builder) => {
        builder
            // Start conversation
            .addCase(startConversation.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(startConversation.fulfilled, (state, action) => {
                state.isLoading = false;
                const conversation = action.payload;
                
                // Add or update conversation
                const existingIndex = state.conversations.findIndex(c => c._id === conversation._id);
                if (existingIndex > -1) {
                    state.conversations[existingIndex] = conversation;
                } else {
                    state.conversations.unshift(conversation);
                }
                
                state.currentConversation = conversation;
            })
            .addCase(startConversation.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload as string;
            })

            // Fetch conversations
            .addCase(fetchConversations.pending, (state) => {
                state.isLoading = true;
                state.error = null;
            })
            .addCase(fetchConversations.fulfilled, (state, action) => {
                state.isLoading = false;
                state.conversations = action.payload.conversations;
            })
            .addCase(fetchConversations.rejected, (state, action) => {
                state.isLoading = false;
                state.error = action.payload as string;
            })

            // Fetch conversation messages
            .addCase(fetchConversationMessages.pending, (state) => {
                state.isLoadingMessages = true;
                state.error = null;
            })
            .addCase(fetchConversationMessages.fulfilled, (state, action) => {
                state.isLoadingMessages = false;
                const { conversationId, messages, pagination } = action.payload;
                
                if (pagination.currentPage === 1) {
                    // First page - replace messages
                    state.messages[conversationId] = messages;
                } else {
                    // Additional pages - prepend messages (older messages)
                    const existingMessages = state.messages[conversationId] || [];
                    state.messages[conversationId] = [...messages, ...existingMessages];
                }
                
                state.pagination[conversationId] = pagination;
            })
            .addCase(fetchConversationMessages.rejected, (state, action) => {
                state.isLoadingMessages = false;
                state.error = action.payload as string;
            })

            // Send message (REST backup)
            .addCase(sendMessageREST.fulfilled, (state, action) => {
                const { conversationId, message } = action.payload;
                
                if (!state.messages[conversationId]) {
                    state.messages[conversationId] = [];
                }
                
                // Avoid duplicates
                const exists = state.messages[conversationId].some(m => m._id === message._id);
                if (!exists) {
                    state.messages[conversationId].push(message);
                }
            })

            // Fetch unread count
            .addCase(fetchUnreadCount.fulfilled, (state, action) => {
                state.totalUnreadCount = action.payload;
            });
    }
});

export const {
    addMessage,
    markMessagesAsRead,
    updateTypingStatus,
    updateUserOnlineStatus,
    incrementUnreadCount,
    setCurrentConversation,
    clearError,
    clearConversationMessages
} = chatSlice.actions;

export default chatSlice.reducer;
