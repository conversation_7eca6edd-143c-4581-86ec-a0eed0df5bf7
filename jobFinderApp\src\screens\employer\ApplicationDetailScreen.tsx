// jobFinderApp/src/screens/employer/ApplicationDetailScreen.tsx

import React, { useEffect, useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    ActivityIndicator,
    Alert,
    Modal,
    TextInput,
    Linking,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useDispatch, useSelector } from 'react-redux';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import { AppDispatch, RootState } from '../../redux/store';
import {
    fetchApplicationById,
    updateApplicationStatus,
    addApplicationNotes,
    clearCurrentApplication,
    clearError
} from '../../redux/employer/employerApplicationsSlice';
import { startConversation } from '../../redux/chat/chatSlice';
import { JobApplication } from '../../services/applicationService';
import { EmployerStackParamList } from '../../navigation/EmployerStack';

type ApplicationDetailNavigationProp = NativeStackNavigationProp<EmployerStackParamList>;
type ApplicationDetailRouteProp = RouteProp<EmployerStackParamList, 'ApplicationDetail'>;

const APPLICATION_STATUS_COLORS = {
    pending: '#FFA500',
    reviewed: '#2196F3',
    shortlisted: '#4CAF50',
    rejected: '#F44336',
    hired: '#8BC34A'
};

const APPLICATION_STATUS_LABELS = {
    pending: 'Pending',
    reviewed: 'Reviewed',
    shortlisted: 'Shortlisted',
    rejected: 'Rejected',
    hired: 'Hired'
};

const ApplicationDetailScreen: React.FC = () => {
    const { theme } = useTheme();
    const navigation = useNavigation<ApplicationDetailNavigationProp>();
    const route = useRoute<ApplicationDetailRouteProp>();
    const dispatch = useDispatch<AppDispatch>();

    const { applicationId } = route.params;

    const {
        currentApplication,
        isLoading,
        error,
        updating
    } = useSelector((state: RootState) => state.employerApplications);

    const [statusModalVisible, setStatusModalVisible] = useState(false);
    const [notesModalVisible, setNotesModalVisible] = useState(false);
    const [notes, setNotes] = useState('');
    const [rating, setRating] = useState(0);

    // Load application on mount
    useEffect(() => {
        if (applicationId) {
            dispatch(fetchApplicationById(applicationId));
        }
        return () => {
            dispatch(clearCurrentApplication());
        };
    }, [dispatch, applicationId]);

    // Update notes when application loads
    useEffect(() => {
        if (currentApplication) {
            setNotes(currentApplication.employerNotes || '');
            setRating(currentApplication.rating || 0);
            navigation.setOptions({
                title: `${currentApplication.applicant.firstName} ${currentApplication.applicant.lastName}`,
            });
        }
    }, [currentApplication, navigation]);

    // Clear error on unmount
    useEffect(() => {
        return () => {
            dispatch(clearError());
        };
    }, [dispatch]);

    const handleStatusUpdate = async (newStatus: string, notes?: string) => {
        if (!currentApplication) return;

        try {
            await dispatch(updateApplicationStatus({
                applicationId: currentApplication._id,
                status: newStatus,
                notes
            }));
            setStatusModalVisible(false);
        } catch (error) {
            Alert.alert('Error', 'Failed to update application status');
        }
    };

    const handleNotesUpdate = async () => {
        if (!currentApplication) return;

        try {
            await dispatch(addApplicationNotes({
                applicationId: currentApplication._id,
                notes,
                rating: rating > 0 ? rating : undefined
            }));
            setNotesModalVisible(false);
        } catch (error) {
            Alert.alert('Error', 'Failed to update notes');
        }
    };

    const openResume = async () => {
        if (currentApplication?.resumeUrl) {
            try {
                await Linking.openURL(currentApplication.resumeUrl);
            } catch (error) {
                Alert.alert('Error', 'Could not open resume');
            }
        }
    };

    const handleMessageCandidate = async () => {
        if (!currentApplication) return;

        try {
            const result = await dispatch(startConversation(currentApplication._id));
            if (startConversation.fulfilled.match(result)) {
                navigation.navigate('ChatDetail', { conversationId: result.payload._id });
            }
        } catch (error) {
            console.error('Error starting conversation:', error);
            Alert.alert('Error', 'Failed to start conversation. Please try again.');
        }
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getStatusColor = (status: string) => {
        return APPLICATION_STATUS_COLORS[status as keyof typeof APPLICATION_STATUS_COLORS] || theme.textMuted;
    };

    const getStatusLabel = (status: string) => {
        return APPLICATION_STATUS_LABELS[status as keyof typeof APPLICATION_STATUS_LABELS] || status;
    };

    const renderStarRating = (currentRating: number, onRatingChange?: (rating: number) => void) => {
        return (
            <View style={styles.starContainer}>
                {[1, 2, 3, 4, 5].map((star) => (
                    <TouchableOpacity
                        key={star}
                        onPress={() => onRatingChange?.(star)}
                        disabled={!onRatingChange}
                    >
                        <Ionicons
                            name={star <= currentRating ? 'star' : 'star-outline'}
                            size={20}
                            color={star <= currentRating ? '#FFD700' : theme.textMuted}
                        />
                    </TouchableOpacity>
                ))}
            </View>
        );
    };
    // Loading state
    if (isLoading) {
        return (
            <View style={[styles.container, styles.centered, { backgroundColor: theme.background }]}>
                <ActivityIndicator size="large" color={theme.primary} />
                <Text style={[styles.loadingText, { color: theme.text }]}>Loading application details...</Text>
            </View>
        );
    }

    // Error state
    if (!currentApplication && !isLoading) {
        return (
            <View style={[styles.container, styles.centered, { backgroundColor: theme.background }]}>
                <Ionicons name="alert-circle-outline" size={64} color={theme.error} />
                <Text style={[styles.errorTitle, { color: theme.text }]}>Application Not Found</Text>
                <Text style={[styles.errorText, { color: theme.textSecondary }]}>
                    The application you're looking for could not be found.
                </Text>
                <TouchableOpacity
                    style={[styles.button, { backgroundColor: theme.primary }]}
                    onPress={() => navigation.goBack()}
                >
                    <Text style={styles.buttonText}>Go Back</Text>
                </TouchableOpacity>
            </View>
        );
    }

    if (!currentApplication) return null;

    const statusColor = getStatusColor(currentApplication.status);

    return (
        <View style={[styles.container, { backgroundColor: theme.background }]}>
            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                {/* Header Section */}
                <View style={[styles.headerSection, { backgroundColor: theme.surface }]}>
                    <View style={styles.applicantHeader}>
                        <View style={[styles.avatar, { backgroundColor: theme.primary + '20' }]}>
                            <Text style={[styles.avatarText, { color: theme.primary }]}>
                                {currentApplication.applicant.firstName[0]}{currentApplication.applicant.lastName[0]}
                            </Text>
                        </View>
                        <View style={styles.applicantInfo}>
                            <Text style={[styles.applicantName, { color: theme.text }]}>
                                {currentApplication.applicant.firstName} {currentApplication.applicant.lastName}
                            </Text>
                            <Text style={[styles.applicantEmail, { color: theme.textSecondary }]}>
                                {currentApplication.applicant.email}
                            </Text>
                            {currentApplication.applicant.location && (
                                <Text style={[styles.applicantLocation, { color: theme.textMuted }]}>
                                    <Ionicons name="location-outline" size={14} color={theme.textMuted} />
                                    {' '}{currentApplication.applicant.location}
                                </Text>
                            )}
                        </View>
                    </View>

                    <View style={styles.statusSection}>
                        <View style={[styles.statusBadge, { backgroundColor: statusColor + '20' }]}>
                            <Text style={[styles.statusText, { color: statusColor }]}>
                                {getStatusLabel(currentApplication.status)}
                            </Text>
                        </View>
                        <TouchableOpacity
                            style={[styles.changeStatusButton, { borderColor: theme.primary }]}
                            onPress={() => setStatusModalVisible(true)}
                        >
                            <Text style={[styles.changeStatusText, { color: theme.primary }]}>Change Status</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Application Info */}
                <View style={[styles.section, { backgroundColor: theme.surface }]}>
                    <Text style={[styles.sectionTitle, { color: theme.text }]}>Application Information</Text>

                    <View style={styles.infoRow}>
                        <Text style={[styles.infoLabel, { color: theme.textSecondary }]}>Job Position:</Text>
                        <Text style={[styles.infoValue, { color: theme.text }]}>{currentApplication.job.title}</Text>
                    </View>

                    <View style={styles.infoRow}>
                        <Text style={[styles.infoLabel, { color: theme.textSecondary }]}>Company:</Text>
                        <Text style={[styles.infoValue, { color: theme.text }]}>{currentApplication.job.company}</Text>
                    </View>

                    <View style={styles.infoRow}>
                        <Text style={[styles.infoLabel, { color: theme.textSecondary }]}>Applied On:</Text>
                        <Text style={[styles.infoValue, { color: theme.text }]}>{formatDate(currentApplication.appliedAt)}</Text>
                    </View>

                    <View style={styles.infoRow}>
                        <Text style={[styles.infoLabel, { color: theme.textSecondary }]}>Last Updated:</Text>
                        <Text style={[styles.infoValue, { color: theme.text }]}>{formatDate(currentApplication.updatedAt)}</Text>
                    </View>
                </View>

                {/* Cover Letter */}
                {currentApplication.coverLetter && (
                    <View style={[styles.section, { backgroundColor: theme.surface }]}>
                        <Text style={[styles.sectionTitle, { color: theme.text }]}>Cover Letter</Text>
                        <Text style={[styles.coverLetter, { color: theme.textSecondary }]}>
                            {currentApplication.coverLetter}
                        </Text>
                    </View>
                )}

                {/* Resume */}
                {currentApplication.resumeUrl && (
                    <View style={[styles.section, { backgroundColor: theme.surface }]}>
                        <Text style={[styles.sectionTitle, { color: theme.text }]}>Resume</Text>
                        <TouchableOpacity
                            style={[styles.resumeButton, { backgroundColor: theme.primary + '10', borderColor: theme.primary }]}
                            onPress={openResume}
                        >
                            <Ionicons name="document-text-outline" size={20} color={theme.primary} />
                            <Text style={[styles.resumeButtonText, { color: theme.primary }]}>View Resume</Text>
                            <Ionicons name="open-outline" size={16} color={theme.primary} />
                        </TouchableOpacity>
                    </View>
                )}

                {/* Rating & Notes */}
                <View style={[styles.section, { backgroundColor: theme.surface }]}>
                    <View style={styles.sectionHeader}>
                        <Text style={[styles.sectionTitle, { color: theme.text }]}>Your Assessment</Text>
                        <TouchableOpacity
                            style={[styles.editButton, { backgroundColor: theme.info + '10' }]}
                            onPress={() => setNotesModalVisible(true)}
                        >
                            <Ionicons name="create-outline" size={16} color={theme.info} />
                            <Text style={[styles.editButtonText, { color: theme.info }]}>Edit</Text>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.ratingSection}>
                        <Text style={[styles.ratingLabel, { color: theme.textSecondary }]}>Rating:</Text>
                        {renderStarRating(currentApplication.rating || 0)}
                    </View>

                    {currentApplication.employerNotes ? (
                        <View style={styles.notesSection}>
                            <Text style={[styles.notesLabel, { color: theme.textSecondary }]}>Notes:</Text>
                            <Text style={[styles.notesText, { color: theme.text }]}>
                                {currentApplication.employerNotes}
                            </Text>
                        </View>
                    ) : (
                        <Text style={[styles.noNotesText, { color: theme.textMuted }]}>
                            No notes added yet. Tap Edit to add your assessment.
                        </Text>
                    )}
                </View>
            </ScrollView>

            {/* Action Buttons */}
            <View style={[styles.actionBar, { backgroundColor: theme.surface, borderTopColor: theme.border }]}>
                <TouchableOpacity
                    style={[styles.actionButton, styles.messageButton, { backgroundColor: theme.info }]}
                    onPress={handleMessageCandidate}
                >
                    <Ionicons name="chatbubble-outline" size={20} color="#fff" />
                    <Text style={styles.actionButtonText}>Message</Text>
                </TouchableOpacity>

                <TouchableOpacity
                    style={[styles.actionButton, styles.rejectButton, { backgroundColor: theme.error }]}
                    onPress={() => handleStatusUpdate('rejected')}
                    disabled={updating || currentApplication.status === 'rejected'}
                >
                    {updating ? (
                        <ActivityIndicator size="small" color="#fff" />
                    ) : (
                        <>
                            <Ionicons name="close-circle-outline" size={20} color="#fff" />
                            <Text style={styles.actionButtonText}>Reject</Text>
                        </>
                    )}
                </TouchableOpacity>

                <TouchableOpacity
                    style={[styles.actionButton, styles.shortlistButton, { backgroundColor: theme.warning }]}
                    onPress={() => handleStatusUpdate('shortlisted')}
                    disabled={updating || currentApplication.status === 'shortlisted'}
                >
                    {updating ? (
                        <ActivityIndicator size="small" color="#fff" />
                    ) : (
                        <>
                            <Ionicons name="bookmark-outline" size={20} color="#fff" />
                            <Text style={styles.actionButtonText}>Shortlist</Text>
                        </>
                    )}
                </TouchableOpacity>

                <TouchableOpacity
                    style={[styles.actionButton, styles.hireButton, { backgroundColor: theme.success }]}
                    onPress={() => handleStatusUpdate('hired')}
                    disabled={updating || currentApplication.status === 'hired'}
                >
                    {updating ? (
                        <ActivityIndicator size="small" color="#fff" />
                    ) : (
                        <>
                            <Ionicons name="checkmark-circle-outline" size={20} color="#fff" />
                            <Text style={styles.actionButtonText}>Hire</Text>
                        </>
                    )}
                </TouchableOpacity>
            </View>
            {/* Status Modal */}
            <Modal
                visible={statusModalVisible}
                transparent
                animationType="slide"
                onRequestClose={() => setStatusModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={[styles.modalContent, { backgroundColor: theme.surface }]}>
                        <View style={styles.modalHeader}>
                            <Text style={[styles.modalTitle, { color: theme.text }]}>Change Status</Text>
                            <TouchableOpacity
                                style={styles.modalCloseButton}
                                onPress={() => setStatusModalVisible(false)}
                            >
                                <Ionicons name="close" size={24} color={theme.textSecondary} />
                            </TouchableOpacity>
                        </View>

                        <View style={styles.statusOptions}>
                            {Object.entries(APPLICATION_STATUS_LABELS).map(([status, label]) => (
                                <TouchableOpacity
                                    key={status}
                                    style={[
                                        styles.statusOption,
                                        {
                                            backgroundColor: currentApplication.status === status
                                                ? getStatusColor(status) + '20'
                                                : theme.background,
                                            borderColor: getStatusColor(status)
                                        }
                                    ]}
                                    onPress={() => handleStatusUpdate(status)}
                                    disabled={updating}
                                >
                                    <Text style={[
                                        styles.statusOptionText,
                                        { color: getStatusColor(status) }
                                    ]}>
                                        {label}
                                    </Text>
                                    {currentApplication.status === status && (
                                        <Ionicons name="checkmark" size={16} color={getStatusColor(status)} />
                                    )}
                                </TouchableOpacity>
                            ))}
                        </View>
                    </View>
                </View>
            </Modal>

            {/* Notes Modal */}
            <Modal
                visible={notesModalVisible}
                transparent
                animationType="slide"
                onRequestClose={() => setNotesModalVisible(false)}
            >
                <View style={styles.modalOverlay}>
                    <View style={[styles.modalContent, { backgroundColor: theme.surface }]}>
                        <View style={styles.modalHeader}>
                            <Text style={[styles.modalTitle, { color: theme.text }]}>Assessment</Text>
                            <TouchableOpacity
                                style={styles.modalCloseButton}
                                onPress={() => setNotesModalVisible(false)}
                            >
                                <Ionicons name="close" size={24} color={theme.textSecondary} />
                            </TouchableOpacity>
                        </View>

                        <View style={styles.modalBody}>
                            <View style={styles.ratingSection}>
                                <Text style={[styles.ratingLabel, { color: theme.textSecondary }]}>Rating:</Text>
                                {renderStarRating(rating, setRating)}
                            </View>

                            <Text style={[styles.notesLabel, { color: theme.textSecondary }]}>Notes:</Text>
                            <TextInput
                                style={[
                                    styles.notesInput,
                                    {
                                        backgroundColor: theme.background,
                                        borderColor: theme.border,
                                        color: theme.text
                                    }
                                ]}
                                value={notes}
                                onChangeText={setNotes}
                                placeholder="Add your notes about this applicant..."
                                placeholderTextColor={theme.textMuted}
                                multiline
                                numberOfLines={4}
                                textAlignVertical="top"
                            />

                            <View style={styles.modalActions}>
                                <TouchableOpacity
                                    style={[styles.modalButton, styles.cancelButton, { borderColor: theme.border }]}
                                    onPress={() => setNotesModalVisible(false)}
                                >
                                    <Text style={[styles.modalButtonText, { color: theme.text }]}>Cancel</Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={[styles.modalButton, { backgroundColor: theme.primary }]}
                                    onPress={handleNotesUpdate}
                                    disabled={updating}
                                >
                                    {updating ? (
                                        <ActivityIndicator size="small" color="#fff" />
                                    ) : (
                                        <Text style={styles.modalButtonText}>Save</Text>
                                    )}
                                </TouchableOpacity>
                            </View>
                        </View>
                    </View>
                </View>
            </Modal>

            {/* Error Display */}
            {error && (
                <View style={[styles.errorContainer, { backgroundColor: theme.error + '20' }]}>
                    <Text style={[styles.errorText, { color: theme.error }]}>{error}</Text>
                    <TouchableOpacity
                        style={styles.errorCloseButton}
                        onPress={() => dispatch(clearError())}
                    >
                        <Ionicons name="close" size={16} color={theme.error} />
                    </TouchableOpacity>
                </View>
            )}
        </View>
    );
};
const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    centered: {
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
    },
    errorTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginTop: 16,
        marginBottom: 8,
    },
    errorText: {
        fontSize: 16,
        textAlign: 'center',
        marginBottom: 24,
    },
    button: {
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
    },
    buttonText: {
        fontSize: 16,
        fontWeight: '600',
        color: '#fff',
    },
    scrollView: {
        flex: 1,
    },
    headerSection: {
        padding: 20,
        marginBottom: 12,
    },
    applicantHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    avatar: {
        width: 60,
        height: 60,
        borderRadius: 30,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    avatarText: {
        fontSize: 20,
        fontWeight: 'bold',
    },
    applicantInfo: {
        flex: 1,
    },
    applicantName: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    applicantEmail: {
        fontSize: 16,
        marginBottom: 4,
    },
    applicantLocation: {
        fontSize: 14,
        flexDirection: 'row',
        alignItems: 'center',
    },
    statusSection: {
        alignItems: 'center',
    },
    statusBadge: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        marginBottom: 12,
    },
    statusText: {
        fontSize: 14,
        fontWeight: '600',
    },
    changeStatusButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 8,
        borderWidth: 1,
    },
    changeStatusText: {
        fontSize: 14,
        fontWeight: '500',
    },
    section: {
        padding: 20,
        marginBottom: 12,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    sectionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    editButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 6,
    },
    editButtonText: {
        fontSize: 12,
        fontWeight: '500',
        marginLeft: 4,
    },
    infoRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    infoLabel: {
        fontSize: 14,
        fontWeight: '500',
        flex: 1,
    },
    infoValue: {
        fontSize: 14,
        flex: 2,
        textAlign: 'right',
    },
    coverLetter: {
        fontSize: 16,
        lineHeight: 24,
    },
    resumeButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 16,
        borderRadius: 8,
        borderWidth: 1,
    },
    resumeButtonText: {
        fontSize: 16,
        fontWeight: '500',
        marginHorizontal: 8,
    },
    ratingSection: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    ratingLabel: {
        fontSize: 14,
        fontWeight: '500',
        marginRight: 12,
    },
    starContainer: {
        flexDirection: 'row',
        gap: 4,
    },
    notesSection: {
        marginTop: 8,
    },
    notesLabel: {
        fontSize: 14,
        fontWeight: '500',
        marginBottom: 8,
    },
    notesText: {
        fontSize: 16,
        lineHeight: 24,
    },
    noNotesText: {
        fontSize: 14,
        fontStyle: 'italic',
        textAlign: 'center',
        marginTop: 8,
    },
    actionBar: {
        flexDirection: 'row',
        padding: 16,
        borderTopWidth: 1,
        gap: 12,
    },
    actionButton: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 12,
        borderRadius: 8,
        gap: 8,
    },
    actionButtonText: {
        fontSize: 14,
        fontWeight: '600',
        color: '#fff',
    },
    messageButton: {
        // backgroundColor set dynamically
    },
    rejectButton: {
        // backgroundColor set dynamically
    },
    shortlistButton: {
        // backgroundColor set dynamically
    },
    hireButton: {
        // backgroundColor set dynamically
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    modalContent: {
        borderRadius: 12,
        padding: 20,
        width: '100%',
        maxWidth: 400,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: '600',
    },
    modalCloseButton: {
        padding: 4,
    },
    modalBody: {
        alignItems: 'stretch',
    },
    statusOptions: {
        gap: 8,
    },
    statusOption: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 12,
        borderRadius: 8,
        borderWidth: 1,
    },
    statusOptionText: {
        fontSize: 14,
        fontWeight: '500',
    },
    notesInput: {
        borderWidth: 1,
        borderRadius: 8,
        padding: 12,
        fontSize: 14,
        height: 100,
        marginBottom: 20,
    },
    modalActions: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: 12,
    },
    modalButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
    },
    cancelButton: {
        backgroundColor: 'transparent',
        borderWidth: 1,
    },
    modalButtonText: {
        fontSize: 14,
        fontWeight: '600',
        color: '#fff',
    },
    errorContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 12,
        margin: 16,
        borderRadius: 8,
    },
    errorCloseButton: {
        padding: 4,
    },
});

export default ApplicationDetailScreen;
