// backend/src/socket/socketAuth.ts

import jwt from 'jsonwebtoken';
import { Socket } from 'socket.io';
import { JWT_SECRET } from '../config/env';
import User, { IUser } from '../models/User';

export interface AuthenticatedSocket extends Socket {
    user?: IUser;
}

export const authenticateSocket = async (socket: AuthenticatedSocket, next: (err?: Error) => void) => {
    try {
        console.log('🔌 [DEBUG] Socket authentication attempt');
        console.log('🔌 [DEBUG] Handshake auth:', socket.handshake.auth);
        console.log('🔌 [DEBUG] Handshake query:', socket.handshake.query);

        // Get token from handshake auth or query
        const token = socket.handshake.auth.token || socket.handshake.query.token;

        if (!token) {
            console.log('🔌 [ERROR] No authentication token provided');
            return next(new Error('Authentication token required'));
        }

        console.log('🔌 [DEBUG] Token received:', token ? 'Yes' : 'No');

        if (!JWT_SECRET) {
            console.log('🔌 [ERROR] JWT_SECRET not configured');
            return next(new Error('Server configuration error'));
        }

        // Verify JWT token
        console.log('🔌 [DEBUG] Verifying JWT token...');
        const decoded: any = jwt.verify(token as string, JWT_SECRET);
        console.log('🔌 [DEBUG] JWT decoded successfully, user ID:', decoded.id);

        // Find user in database
        const user = await User.findById(decoded.id).select('-passwordHash');

        if (!user) {
            console.log('🔌 [ERROR] User not found in database:', decoded.id);
            return next(new Error('User not found'));
        }

        console.log('🔌 [SUCCESS] Socket authentication successful for user:', user.email);

        // Attach user to socket
        socket.user = user;
        next();
    } catch (error: any) {
        console.error('Socket authentication error:', error.message);
        
        if (error.name === 'TokenExpiredError') {
            return next(new Error('Token expired'));
        } else if (error.name === 'JsonWebTokenError') {
            return next(new Error('Invalid token'));
        }
        
        return next(new Error('Authentication failed'));
    }
};

export const requireAuth = (socket: AuthenticatedSocket): boolean => {
    return !!socket.user;
};

export const requireRole = (socket: AuthenticatedSocket, allowedRoles: string[]): boolean => {
    if (!socket.user) return false;
    return allowedRoles.includes(socket.user.role);
};

export const getUserId = (socket: AuthenticatedSocket): string | null => {
    return socket.user?._id?.toString() || null;
};

export const getUserRole = (socket: AuthenticatedSocket): string | null => {
    return socket.user?.role || null;
};
