// src/navigation/AppStack.tsx

import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { AppStackParamList } from './types';

// Import screens that will exist OUTSIDE the bottom tabs
import JobDetailScreen from '../screens/jobs/JobDetailScreen';
import ApplicationScreen from '../screens/ApplicationScreen';
import MyApplicationsScreen from '../screens/MyApplicationsScreen';
import EditProfileScreen from '../screens/profile/EditProfileScreen';
import PasswordChangeScreen from '../screens/profile/PasswordChangeScreen';
import PrivacySettingsScreen from '../screens/profile/PrivacySettingsScreen';
import HelpSupportScreen from '../screens/profile/HelpSupportScreen';
import AboutScreen from '../screens/settings/AboutScreen';
import CategoryScreen from '../screens/jobs/CategoryScreen';
import ChatListScreen from '../screens/chat/ChatListScreen';
import ChatDetailScreen from '../screens/chat/ChatDetailScreen';

// Import your new MainTabNavigator
import MainTabNavigator from './MainTabNavigator';

const AppStack = createNativeStackNavigator<AppStackParamList>();

const AppStackNavigator: React.FC = () => {
  return (
    <AppStack.Navigator screenOptions={{ headerShown: false }}>
      <AppStack.Screen name="MainTabs" component={MainTabNavigator} />
      <AppStack.Screen name="JobDetail" component={JobDetailScreen} />
      <AppStack.Screen name="Application" component={ApplicationScreen} />
      <AppStack.Screen
        name="MyApplications"
        component={MyApplicationsScreen}
        options={{ headerShown: true, title: 'My Applications' }}
      />
      <AppStack.Screen name="EditProfile" component={EditProfileScreen} />
      <AppStack.Screen name="PasswordChange" component={PasswordChangeScreen} />
      <AppStack.Screen name="PrivacySettings" component={PrivacySettingsScreen} />
      <AppStack.Screen name="HelpSupport" component={HelpSupportScreen} />
      <AppStack.Screen name="About" component={AboutScreen} />
      <AppStack.Screen name="CategoryScreen" component={CategoryScreen} />
      <AppStack.Screen
        name="ChatList"
        component={ChatListScreen}
        options={{ headerShown: true, title: 'Messages' }}
      />
      <AppStack.Screen
        name="ChatDetail"
        component={ChatDetailScreen}
        options={{ headerShown: true, title: 'Chat' }}
      />
    </AppStack.Navigator>
  );
};

export default AppStackNavigator;