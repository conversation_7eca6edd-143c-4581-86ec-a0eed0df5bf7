import React, { useEffect } from 'react';
import { NavigationContainer, LinkingOptions } from '@react-navigation/native'; // Import LinkingOptions
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import AuthNavigator from './AuthStack';
import AppNavigator from './AppStack';
import EmployerStack from './EmployerStack';
import { RootState, AppDispatch } from '../redux/store';
import { useSelector, useDispatch } from 'react-redux';
import { RootStackParamList } from './types';
import { View, Text, ActivityIndicator } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage'; // Import AsyncStorage
import { setToken, setUser, loginAsGuest, fetchUserProfile } from '../redux/auth/authSlice'; // Import setToken and setUser actions
import { useTheme } from '../contexts/ThemeContext';
import socketService from '../services/socketService';

const RootStack = createNativeStackNavigator<RootStackParamList>();

// Define your deep linking configuration
// IMPORTANT: Replace 'jobfinderapp' with your actual custom URL scheme
// and 'https://jobfinderapp.com' with your actual website domain for universal links
const linking: LinkingOptions<RootStackParamList> = {
    prefixes: ['jobfinderapp://', 'https://jobfinderapp.com'], // Add your deep link prefixes
    config: {
        screens: {
            // 'Auth' here refers to the name of your AuthNavigator component in RootStack.
            Auth: {
                screens: {
                    // This maps the URL path 'reset-password/:token' to the 'ResetPassword' screen
                    // within your AuthStack. The ':token' part captures the token from the URL.
                    ResetPassword: 'reset-password/:token',
                    // If your backend sends the token as a query parameter (e.g., ?token=xyz)
                    // instead of a path parameter, you would use this config:
                    // ResetPassword: {
                    //   path: 'reset-password',
                    //   parse: {
                    //     token: (token) => token, // This will parse query params like ?token=xyz
                    //   },
                    // },
                },
            },
            // You can also define linking for your AppStack screens if needed
            // App: {
            //   screens: {
            //     Home: 'home',
            //     JobDetail: 'job/:id',
            //   },
            // },
        },
    },
    // Optional: You can add getInitialURL and subscribe for more advanced linking scenarios
    // if default behavior isn't enough, but usually not needed for simple reset links.
};

const RootNavigator: React.FC = () => {
    const dispatch = useDispatch<AppDispatch>(); // Get the dispatch function
    const isAuthenticated = useSelector((state: RootState) => state.auth.isAuthenticated);
    const authIsLoading = useSelector((state: RootState) => state.auth.isLoading);
    const user = useSelector((state: RootState) => state.auth.user);
    const { theme } = useTheme();

    const [isAppInitializing, setIsAppInitializing] = React.useState(true);

    useEffect(() => {
        const checkInitialAuthAndLoadUser = async () => {
            console.log('🔄 [DEBUG] RootNavigator: Checking initial auth state...');

            try {
                // Attempt to load token and user data from AsyncStorage
                const storedToken = await AsyncStorage.getItem('userToken');
                const storedUser = await AsyncStorage.getItem('userData');

                console.log('🔄 [DEBUG] RootNavigator: Stored token exists:', !!storedToken);
                console.log('🔄 [DEBUG] RootNavigator: Stored user exists:', !!storedUser);

                if (storedToken) {
                    // Check if it's a guest token
                    if (storedToken === 'guest-token') {
                        console.log('👤 [DEBUG] RootNavigator: Logging in as guest');
                        dispatch(loginAsGuest());
                    } else {
                        console.log('🔑 [DEBUG] RootNavigator: Setting stored token and user');
                        dispatch(setToken(storedToken));
                        if (storedUser) {
                            try {
                                const userData = JSON.parse(storedUser);
                                dispatch(setUser(userData));
                                console.log('✅ [DEBUG] RootNavigator: User data restored from storage');
                            } catch (parseError) {
                                console.error('❌ [DEBUG] RootNavigator: Failed to parse stored user data:', parseError);
                                // Clear corrupted user data
                                await AsyncStorage.removeItem('userData');
                            }
                        }
                    }
                } else {
                    console.log('🔄 [DEBUG] RootNavigator: No stored token found, user needs to authenticate');
                }
            } catch (e) {
                console.error("❌ [DEBUG] RootNavigator: Failed to load initial authentication state:", e);
                // Clear storage if corrupted
                try {
                    await AsyncStorage.clear();
                    console.log('🧹 [DEBUG] RootNavigator: Cleared corrupted storage');
                } catch (clearError) {
                    console.error('❌ [DEBUG] RootNavigator: Failed to clear storage:', clearError);
                }
            } finally {
                console.log('✅ [DEBUG] RootNavigator: Auth initialization complete');
                setIsAppInitializing(false);
            }
        };

        // Only run once
        checkInitialAuthAndLoadUser();
    }, []); // Remove dispatch from dependencies to prevent re-runs

    // Get token from Redux state
    const token = useSelector((state: RootState) => state.auth.token);

    // Initialize socket service when user is authenticated
    useEffect(() => {
        if (isAuthenticated && token && token !== 'guest-token') {
            console.log('🔌 [DEBUG] RootNavigator: Initializing socket connection');
            socketService.connect(token);
        } else if (!isAuthenticated) {
            console.log('🔌 [DEBUG] RootNavigator: Disconnecting socket');
            socketService.disconnect();
        }
    }, [isAuthenticated, token]);

    const overallLoading = isAppInitializing || authIsLoading;

    console.log('🔄 [DEBUG] RootNavigator render state:', {
        isAppInitializing,
        authIsLoading,
        overallLoading,
        isAuthenticated
    });

    if (overallLoading) {
        console.log('⏳ [DEBUG] RootNavigator: Showing loading screen');
        return (
            <View style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: theme.background
            }}>
                <ActivityIndicator size="large" color={theme.primary} />
                <Text style={{
                    marginTop: 20,
                    fontSize: 18,
                    color: theme.text
                }}>
                    {isAppInitializing ? 'Initializing...' : 'Loading Application...'}
                </Text>
                {__DEV__ && (
                    <Text style={{
                        marginTop: 10,
                        fontSize: 12,
                        color: theme.textSecondary
                    }}>
                        Debug: Init={isAppInitializing.toString()}, Auth={authIsLoading.toString()}
                    </Text>
                )}
            </View>
        );
    }

    console.log('🧭 [DEBUG] RootNavigator: Rendering navigation with isAuthenticated:', isAuthenticated);

    return (
        <NavigationContainer
            linking={linking}
            onStateChange={(state) => {
                if (__DEV__) {
                    console.log('🧭 [DEBUG] Navigation state changed:', state?.routes?.[0]?.name);
                }
            }}
        >
            <RootStack.Navigator screenOptions={{ headerShown: false }}>
                <RootStack.Group>
                    {isAuthenticated ? (
                        user?.role === 'employer' ? (
                            <RootStack.Screen name="App" component={EmployerStack} />
                        ) : (
                            <RootStack.Screen name="App" component={AppNavigator} />
                        )
                    ) : (
                        <RootStack.Screen name="Auth" component={AuthNavigator} />
                    )}
                </RootStack.Group>
            </RootStack.Navigator>
        </NavigationContainer>
    );
};

export default RootNavigator;