// backend/src/routes/chatRoutes.ts

import { Router } from 'express';
import { chatController } from '../controllers/chatController';
import authMiddleware from '../middleware/authMiddleware';

const router = Router();

// All chat routes require authentication
router.use(authMiddleware);

// POST /api/chat/conversations/start - Initialize chat after application
router.post('/conversations/start', chatController.startConversation);

// GET /api/chat/conversations - Get user's conversation list
router.get('/conversations', chatController.getConversations);

// GET /api/chat/conversations/:id/messages - Fetch conversation messages
router.get('/conversations/:conversationId/messages', chatController.getConversationMessages);

// POST /api/chat/messages - Send new message (REST backup to Socket.IO)
router.post('/messages', chatController.sendMessage);

// PATCH /api/chat/conversations/:id/read - Mark messages as read
router.patch('/conversations/:conversationId/read', chatController.markMessagesAsRead);

// GET /api/chat/unread-count - Get total unread count
router.get('/unread-count', chatController.getUnreadCount);

export default router;
