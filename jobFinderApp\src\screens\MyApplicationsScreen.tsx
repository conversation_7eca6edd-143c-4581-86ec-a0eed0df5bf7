// jobFinderApp/src/screens/MyApplicationsScreen.tsx

import React, { useEffect, useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    FlatList,
    TouchableOpacity,
    ActivityIndicator,
    RefreshControl,
    Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useDispatch, useSelector } from 'react-redux';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../contexts/ThemeContext';
import { AppDispatch, RootState } from '../redux/store';
import { fetchUserApplications } from '../redux/applications';
import { startConversation } from '../redux/chat/chatSlice';
import { AppStackParamList } from '../types/navigation';

type MyApplicationsNavigationProp = NativeStackNavigationProp<AppStackParamList>;

const APPLICATION_STATUS_COLORS = {
    pending: '#FFA500',
    reviewed: '#2196F3',
    shortlisted: '#4CAF50',
    rejected: '#F44336',
    hired: '#8BC34A'
};

const APPLICATION_STATUS_LABELS = {
    pending: 'Pending Review',
    reviewed: 'Under Review',
    shortlisted: 'Shortlisted',
    rejected: 'Not Selected',
    hired: 'Hired'
};

const MyApplicationsScreen: React.FC = () => {
    const { theme } = useTheme();
    const navigation = useNavigation<MyApplicationsNavigationProp>();
    const dispatch = useDispatch<AppDispatch>();
    
    const { applications, isLoading, error } = useSelector((state: RootState) => state.applications);
    const [refreshing, setRefreshing] = useState(false);

    useEffect(() => {
        loadApplications();
    }, []);

    const loadApplications = async () => {
        try {
            await dispatch(fetchUserApplications());
        } catch (error) {
            console.error('Error loading applications:', error);
        }
    };

    const onRefresh = async () => {
        setRefreshing(true);
        await loadApplications();
        setRefreshing(false);
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    const getStatusColor = (status: string) => {
        return APPLICATION_STATUS_COLORS[status as keyof typeof APPLICATION_STATUS_COLORS] || theme.textMuted;
    };

    const getStatusLabel = (status: string) => {
        return APPLICATION_STATUS_LABELS[status as keyof typeof APPLICATION_STATUS_LABELS] || status;
    };

    const handleMessageEmployer = async (applicationId: string) => {
        try {
            const result = await dispatch(startConversation(applicationId));
            if (startConversation.fulfilled.match(result)) {
                navigation.navigate('ChatDetail', { conversationId: result.payload._id });
            }
        } catch (error) {
            console.error('Error starting conversation:', error);
            Alert.alert('Error', 'Failed to start conversation. Please try again.');
        }
    };

    const renderApplicationCard = ({ item }: { item: any }) => {
        const statusColor = getStatusColor(item.status);
        
        return (
            <TouchableOpacity
                style={[styles.applicationCard, { backgroundColor: theme.surface, borderColor: theme.border }]}
                onPress={() => {
                    // Navigate to application detail screen
                    Alert.alert('Application Details', 'Application detail screen will be implemented soon.');
                }}
            >
                <View style={styles.cardHeader}>
                    <View style={styles.jobInfo}>
                        <Text style={[styles.jobTitle, { color: theme.text }]} numberOfLines={1}>
                            {item.job?.title || 'Job Title'}
                        </Text>
                        <Text style={[styles.companyName, { color: theme.textSecondary }]} numberOfLines={1}>
                            {item.job?.company || 'Company Name'}
                        </Text>
                        <Text style={[styles.location, { color: theme.textMuted }]} numberOfLines={1}>
                            <Ionicons name="location-outline" size={12} color={theme.textMuted} />
                            {' '}{item.job?.location?.humanReadable || 'Location'}
                        </Text>
                    </View>
                    
                    <View style={styles.statusContainer}>
                        <View style={[styles.statusBadge, { backgroundColor: statusColor + '20' }]}>
                            <Text style={[styles.statusText, { color: statusColor }]}>
                                {getStatusLabel(item.status)}
                            </Text>
                        </View>
                    </View>
                </View>

                <View style={styles.cardContent}>
                    <Text style={[styles.appliedDate, { color: theme.textMuted }]}>
                        Applied on {formatDate(item.appliedAt)}
                    </Text>
                    
                    {item.coverLetter && (
                        <Text style={[styles.coverLetterPreview, { color: theme.textSecondary }]} numberOfLines={2}>
                            "{item.coverLetter}"
                        </Text>
                    )}
                    
                    {item.employerNotes && (
                        <View style={[styles.notesContainer, { backgroundColor: theme.background }]}>
                            <Text style={[styles.notesLabel, { color: theme.textMuted }]}>Employer Notes:</Text>
                            <Text style={[styles.notesText, { color: theme.text }]} numberOfLines={2}>
                                {item.employerNotes}
                            </Text>
                        </View>
                    )}
                </View>

                <View style={styles.cardFooter}>
                    <View style={styles.jobTypeContainer}>
                        <Text style={[styles.jobType, { color: theme.primary }]}>
                            {item.job?.jobType || 'Full-time'}
                        </Text>
                        {item.job?.salary && (
                            <Text style={[styles.salary, { color: theme.textSecondary }]}>
                                {item.job.salary}
                            </Text>
                        )}
                    </View>

                    <View style={styles.actionButtons}>
                        {item.status !== 'rejected' && (
                            <TouchableOpacity
                                style={[styles.messageButton, { backgroundColor: theme.info + '10' }]}
                                onPress={() => handleMessageEmployer(item._id)}
                            >
                                <Ionicons name="chatbubble-outline" size={14} color={theme.info} />
                                <Text style={[styles.messageButtonText, { color: theme.info }]}>Message</Text>
                            </TouchableOpacity>
                        )}

                        <TouchableOpacity
                            style={[styles.viewButton, { backgroundColor: theme.primary + '10' }]}
                            onPress={() => {
                                Alert.alert('View Details', 'Application detail view will be implemented soon.');
                            }}
                        >
                            <Text style={[styles.viewButtonText, { color: theme.primary }]}>View Details</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </TouchableOpacity>
        );
    };

    const renderEmptyState = () => (
        <View style={styles.emptyState}>
            <Ionicons name="document-outline" size={64} color={theme.textSecondary} />
            <Text style={[styles.emptyStateTitle, { color: theme.text }]}>No Applications Yet</Text>
            <Text style={[styles.emptyStateText, { color: theme.textSecondary }]}>
                You haven't applied to any jobs yet. Start exploring job opportunities!
            </Text>
            <TouchableOpacity
                style={[styles.exploreButton, { backgroundColor: theme.primary }]}
                onPress={() => navigation.navigate('Jobs' as any)}
            >
                <Text style={styles.exploreButtonText}>Explore Jobs</Text>
            </TouchableOpacity>
        </View>
    );

    if (isLoading && applications.length === 0) {
        return (
            <View style={[styles.container, styles.centered, { backgroundColor: theme.background }]}>
                <ActivityIndicator size="large" color={theme.primary} />
                <Text style={[styles.loadingText, { color: theme.text }]}>Loading your applications...</Text>
            </View>
        );
    }

    return (
        <View style={[styles.container, { backgroundColor: theme.background }]}>
            <FlatList
                data={applications}
                renderItem={renderApplicationCard}
                keyExtractor={(item) => item._id}
                ListEmptyComponent={renderEmptyState}
                refreshControl={
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                }
                contentContainerStyle={applications.length === 0 ? styles.emptyContainer : styles.listContainer}
                showsVerticalScrollIndicator={false}
            />
            
            {error && (
                <View style={[styles.errorContainer, { backgroundColor: theme.error + '20' }]}>
                    <Text style={[styles.errorText, { color: theme.error }]}>{error}</Text>
                    <TouchableOpacity
                        style={styles.errorCloseButton}
                        onPress={() => {
                            // Clear error - you might want to add this action to your slice
                        }}
                    >
                        <Ionicons name="close" size={16} color={theme.error} />
                    </TouchableOpacity>
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    centered: {
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
    },
    listContainer: {
        padding: 16,
    },
    applicationCard: {
        borderRadius: 12,
        borderWidth: 1,
        marginBottom: 16,
        padding: 16,
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    jobInfo: {
        flex: 1,
        marginRight: 12,
    },
    jobTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 4,
    },
    companyName: {
        fontSize: 14,
        marginBottom: 2,
    },
    location: {
        fontSize: 12,
        flexDirection: 'row',
        alignItems: 'center',
    },
    statusContainer: {
        alignItems: 'flex-end',
    },
    statusBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusText: {
        fontSize: 12,
        fontWeight: '600',
    },
    cardContent: {
        marginBottom: 12,
    },
    appliedDate: {
        fontSize: 12,
        marginBottom: 8,
    },
    coverLetterPreview: {
        fontSize: 14,
        fontStyle: 'italic',
        marginBottom: 8,
    },
    notesContainer: {
        padding: 8,
        borderRadius: 6,
        marginTop: 8,
    },
    notesLabel: {
        fontSize: 12,
        fontWeight: '600',
        marginBottom: 4,
    },
    notesText: {
        fontSize: 14,
    },
    cardFooter: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    jobTypeContainer: {
        flex: 1,
    },
    jobType: {
        fontSize: 12,
        fontWeight: '500',
        marginBottom: 2,
    },
    salary: {
        fontSize: 12,
    },
    actionButtons: {
        flexDirection: 'row',
        gap: 8,
    },
    messageButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 10,
        paddingVertical: 6,
        borderRadius: 6,
        gap: 4,
    },
    messageButtonText: {
        fontSize: 12,
        fontWeight: '500',
    },
    viewButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 6,
    },
    viewButtonText: {
        fontSize: 12,
        fontWeight: '500',
    },
    emptyState: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    emptyContainer: {
        flexGrow: 1,
    },
    emptyStateTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginTop: 16,
        marginBottom: 8,
    },
    emptyStateText: {
        fontSize: 14,
        textAlign: 'center',
        paddingHorizontal: 32,
        marginBottom: 24,
    },
    exploreButton: {
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderRadius: 8,
    },
    exploreButtonText: {
        color: '#fff',
        fontSize: 14,
        fontWeight: '600',
    },
    errorContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: 12,
        margin: 16,
        borderRadius: 8,
    },
    errorText: {
        flex: 1,
        fontSize: 14,
    },
    errorCloseButton: {
        padding: 4,
    },
});

export default MyApplicationsScreen;
