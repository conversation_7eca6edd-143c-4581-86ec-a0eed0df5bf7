/*!
 * Engine.IO v6.6.3
 * (c) 2014-2025 <PERSON>
 * Released under the MIT License.
 */
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(t="undefined"!=typeof globalThis?globalThis:t||self).eio=n()}(this,(function(){"use strict";function t(t,n){for(var i=0;i<n.length;i++){var r=n[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,u(r.key),r)}}function n(n,i,r){return i&&t(n.prototype,i),r&&t(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}function i(){return i=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var i=arguments[n];for(var r in i)({}).hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t},i.apply(null,arguments)}function r(t){return r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},r(t)}function e(t,n){t.prototype=Object.create(n.prototype),t.prototype.constructor=t,s(t,n)}function o(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(o=function(){return!!t})()}function s(t,n){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},s(t,n)}function u(t){var n=function(t,n){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,n||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==typeof n?n:n+""}function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function h(t){var n="function"==typeof Map?new Map:void 0;return h=function(t){if(null===t||!function(t){try{return-1!==Function.toString.call(t).indexOf("[native code]")}catch(n){return"function"==typeof t}}(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==n){if(n.has(t))return n.get(t);n.set(t,i)}function i(){return function(t,n,i){if(o())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,n);var e=new(t.bind.apply(t,r));return i&&s(e,i.prototype),e}(t,arguments,r(this).constructor)}return i.prototype=Object.create(t.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),s(i,t)},h(t)}var c=Object.create(null);c.open="0",c.close="1",c.ping="2",c.pong="3",c.message="4",c.upgrade="5",c.noop="6";var a=Object.create(null);Object.keys(c).forEach((function(t){a[c[t]]=t}));var p,l={type:"error",data:"parser error"},v="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),d="function"==typeof ArrayBuffer,y=function(t){return"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer instanceof ArrayBuffer},b=function(t,n,i){var r=t.type,e=t.data;return v&&e instanceof Blob?n?i(e):g(e,i):d&&(e instanceof ArrayBuffer||y(e))?n?i(e):g(new Blob([e]),i):i(c[r]+(e||""))},g=function(t,n){var i=new FileReader;return i.onload=function(){var t=i.result.split(",")[1];n("b"+(t||""))},i.readAsDataURL(t)};function w(t){return t instanceof Uint8Array?t:t instanceof ArrayBuffer?new Uint8Array(t):new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}for(var m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",k="undefined"==typeof Uint8Array?[]:new Uint8Array(256),A=0;A<64;A++)k[m.charCodeAt(A)]=A;var j,B="function"==typeof ArrayBuffer,E=function(t,n){if("string"!=typeof t)return{type:"message",data:U(t,n)};var i=t.charAt(0);return"b"===i?{type:"message",data:O(t.substring(1),n)}:a[i]?t.length>1?{type:a[i],data:t.substring(1)}:{type:a[i]}:l},O=function(t,n){if(B){var i=function(t){var n,i,r,e,o,s=.75*t.length,u=t.length,f=0;"="===t[t.length-1]&&(s--,"="===t[t.length-2]&&s--);var h=new ArrayBuffer(s),c=new Uint8Array(h);for(n=0;n<u;n+=4)i=k[t.charCodeAt(n)],r=k[t.charCodeAt(n+1)],e=k[t.charCodeAt(n+2)],o=k[t.charCodeAt(n+3)],c[f++]=i<<2|r>>4,c[f++]=(15&r)<<4|e>>2,c[f++]=(3&e)<<6|63&o;return h}(t);return U(i,n)}return{base64:!0,data:t}},U=function(t,n){return"blob"===n?t instanceof Blob?t:new Blob([t]):t instanceof ArrayBuffer?t:t.buffer},T=String.fromCharCode(30);function S(){return new TransformStream({transform:function(t,n){!function(t,n){v&&t.data instanceof Blob?t.data.arrayBuffer().then(w).then(n):d&&(t.data instanceof ArrayBuffer||y(t.data))?n(w(t.data)):b(t,!1,(function(t){p||(p=new TextEncoder),n(p.encode(t))}))}(t,(function(i){var r,e=i.length;if(e<126)r=new Uint8Array(1),new DataView(r.buffer).setUint8(0,e);else if(e<65536){r=new Uint8Array(3);var o=new DataView(r.buffer);o.setUint8(0,126),o.setUint16(1,e)}else{r=new Uint8Array(9);var s=new DataView(r.buffer);s.setUint8(0,127),s.setBigUint64(1,BigInt(e))}t.data&&"string"!=typeof t.data&&(r[0]|=128),n.enqueue(r),n.enqueue(i)}))}})}function M(t){return t.reduce((function(t,n){return t+n.length}),0)}function x(t,n){if(t[0].length===n)return t.shift();for(var i=new Uint8Array(n),r=0,e=0;e<n;e++)i[e]=t[0][r++],r===t[0].length&&(t.shift(),r=0);return t.length&&r<t[0].length&&(t[0]=t[0].slice(r)),i}function C(t){if(t)return function(t){for(var n in C.prototype)t[n]=C.prototype[n];return t}(t)}C.prototype.on=C.prototype.addEventListener=function(t,n){return this.t=this.t||{},(this.t["$"+t]=this.t["$"+t]||[]).push(n),this},C.prototype.once=function(t,n){function i(){this.off(t,i),n.apply(this,arguments)}return i.fn=n,this.on(t,i),this},C.prototype.off=C.prototype.removeListener=C.prototype.removeAllListeners=C.prototype.removeEventListener=function(t,n){if(this.t=this.t||{},0==arguments.length)return this.t={},this;var i,r=this.t["$"+t];if(!r)return this;if(1==arguments.length)return delete this.t["$"+t],this;for(var e=0;e<r.length;e++)if((i=r[e])===n||i.fn===n){r.splice(e,1);break}return 0===r.length&&delete this.t["$"+t],this},C.prototype.emit=function(t){this.t=this.t||{};for(var n=new Array(arguments.length-1),i=this.t["$"+t],r=1;r<arguments.length;r++)n[r-1]=arguments[r];if(i){r=0;for(var e=(i=i.slice(0)).length;r<e;++r)i[r].apply(this,n)}return this},C.prototype.emitReserved=C.prototype.emit,C.prototype.listeners=function(t){return this.t=this.t||{},this.t["$"+t]||[]},C.prototype.hasListeners=function(t){return!!this.listeners(t).length};var L="function"==typeof Promise&&"function"==typeof Promise.resolve?function(t){return Promise.resolve().then(t)}:function(t,n){return n(t,0)},R="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function D(t){for(var n=arguments.length,i=new Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];return i.reduce((function(n,i){return t.hasOwnProperty(i)&&(n[i]=t[i]),n}),{})}var P=R.setTimeout,I=R.clearTimeout;function $(t,n){n.useNativeTimers?(t.setTimeoutFn=P.bind(R),t.clearTimeoutFn=I.bind(R)):(t.setTimeoutFn=R.setTimeout.bind(R),t.clearTimeoutFn=R.clearTimeout.bind(R))}function q(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}var N=function(t){function n(n,i,r){var e;return(e=t.call(this,n)||this).description=i,e.context=r,e.type="TransportError",e}return e(n,t),n}(h(Error)),V=function(t){function n(n){var i;return(i=t.call(this)||this).writable=!1,$(i,n),i.opts=n,i.query=n.query,i.socket=n.socket,i.supportsBinary=!n.forceBase64,i}e(n,t);var i=n.prototype;return i.onError=function(n,i,r){return t.prototype.emitReserved.call(this,"error",new N(n,i,r)),this},i.open=function(){return this.readyState="opening",this.doOpen(),this},i.close=function(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this},i.send=function(t){"open"===this.readyState&&this.write(t)},i.onOpen=function(){this.readyState="open",this.writable=!0,t.prototype.emitReserved.call(this,"open")},i.onData=function(t){var n=E(t,this.socket.binaryType);this.onPacket(n)},i.onPacket=function(n){t.prototype.emitReserved.call(this,"packet",n)},i.onClose=function(n){this.readyState="closed",t.prototype.emitReserved.call(this,"close",n)},i.pause=function(t){},i.createUri=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t+"://"+this.i()+this.o()+this.opts.path+this.u(n)},i.i=function(){var t=this.opts.hostname;return-1===t.indexOf(":")?t:"["+t+"]"},i.o=function(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""},i.u=function(t){var n=function(t){var n="";for(var i in t)t.hasOwnProperty(i)&&(n.length&&(n+="&"),n+=encodeURIComponent(i)+"="+encodeURIComponent(t[i]));return n}(t);return n.length?"?"+n:""},n}(C),X=function(t){function i(){var n;return(n=t.apply(this,arguments)||this).h=!1,n}e(i,t);var r=i.prototype;return r.doOpen=function(){this.p()},r.pause=function(t){var n=this;this.readyState="pausing";var i=function(){n.readyState="paused",t()};if(this.h||!this.writable){var r=0;this.h&&(r++,this.once("pollComplete",(function(){--r||i()}))),this.writable||(r++,this.once("drain",(function(){--r||i()})))}else i()},r.p=function(){this.h=!0,this.doPoll(),this.emitReserved("poll")},r.onData=function(t){var n=this;(function(t,n){for(var i=t.split(T),r=[],e=0;e<i.length;e++){var o=E(i[e],n);if(r.push(o),"error"===o.type)break}return r})(t,this.socket.binaryType).forEach((function(t){if("opening"===n.readyState&&"open"===t.type&&n.onOpen(),"close"===t.type)return n.onClose({description:"transport closed by the server"}),!1;n.onPacket(t)})),"closed"!==this.readyState&&(this.h=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this.p())},r.doClose=function(){var t=this,n=function(){t.write([{type:"close"}])};"open"===this.readyState?n():this.once("open",n)},r.write=function(t){var n=this;this.writable=!1,function(t,n){var i=t.length,r=new Array(i),e=0;t.forEach((function(t,o){b(t,!1,(function(t){r[o]=t,++e===i&&n(r.join(T))}))}))}(t,(function(t){n.doWrite(t,(function(){n.writable=!0,n.emitReserved("drain")}))}))},r.uri=function(){var t=this.opts.secure?"https":"http",n=this.query||{};return!1!==this.opts.timestampRequests&&(n[this.opts.timestampParam]=q()),this.supportsBinary||n.sid||(n.b64=1),this.createUri(t,n)},n(i,[{key:"name",get:function(){return"polling"}}])}(V),H=!1;try{H="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){}var F=H;function z(){}var G=function(t){function n(n){var i;if(i=t.call(this,n)||this,"undefined"!=typeof location){var r="https:"===location.protocol,e=location.port;e||(e=r?"443":"80"),i.xd="undefined"!=typeof location&&n.hostname!==location.hostname||e!==n.port}return i}e(n,t);var i=n.prototype;return i.doWrite=function(t,n){var i=this,r=this.request({method:"POST",data:t});r.on("success",n),r.on("error",(function(t,n){i.onError("xhr post error",t,n)}))},i.doPoll=function(){var t=this,n=this.request();n.on("data",this.onData.bind(this)),n.on("error",(function(n,i){t.onError("xhr poll error",n,i)})),this.pollXhr=n},n}(X),J=function(t){function n(n,i,r){var e;return(e=t.call(this)||this).createRequest=n,$(e,r),e.l=r,e.v=r.method||"GET",e.m=i,e.k=void 0!==r.data?r.data:null,e.A(),e}e(n,t);var i=n.prototype;return i.A=function(){var t,i=this,r=D(this.l,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");r.xdomain=!!this.l.xd;var e=this.j=this.createRequest(r);try{e.open(this.v,this.m,!0);try{if(this.l.extraHeaders)for(var o in e.setDisableHeaderCheck&&e.setDisableHeaderCheck(!0),this.l.extraHeaders)this.l.extraHeaders.hasOwnProperty(o)&&e.setRequestHeader(o,this.l.extraHeaders[o])}catch(t){}if("POST"===this.v)try{e.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{e.setRequestHeader("Accept","*/*")}catch(t){}null===(t=this.l.cookieJar)||void 0===t||t.addCookies(e),"withCredentials"in e&&(e.withCredentials=this.l.withCredentials),this.l.requestTimeout&&(e.timeout=this.l.requestTimeout),e.onreadystatechange=function(){var t;3===e.readyState&&(null===(t=i.l.cookieJar)||void 0===t||t.parseCookies(e.getResponseHeader("set-cookie"))),4===e.readyState&&(200===e.status||1223===e.status?i.B():i.setTimeoutFn((function(){i.O("number"==typeof e.status?e.status:0)}),0))},e.send(this.k)}catch(t){return void this.setTimeoutFn((function(){i.O(t)}),0)}"undefined"!=typeof document&&(this.U=n.requestsCount++,n.requests[this.U]=this)},i.O=function(t){this.emitReserved("error",t,this.j),this.T(!0)},i.T=function(t){if(void 0!==this.j&&null!==this.j){if(this.j.onreadystatechange=z,t)try{this.j.abort()}catch(t){}"undefined"!=typeof document&&delete n.requests[this.U],this.j=null}},i.B=function(){var t=this.j.responseText;null!==t&&(this.emitReserved("data",t),this.emitReserved("success"),this.T())},i.abort=function(){this.T()},n}(C);if(J.requestsCount=0,J.requests={},"undefined"!=typeof document)if("function"==typeof attachEvent)attachEvent("onunload",W);else if("function"==typeof addEventListener){addEventListener("onpagehide"in R?"pagehide":"unload",W,!1)}function W(){for(var t in J.requests)J.requests.hasOwnProperty(t)&&J.requests[t].abort()}var K,Q=(K=Z({xdomain:!1}))&&null!==K.responseType,Y=function(t){function n(n){var i;i=t.call(this,n)||this;var r=n&&n.forceBase64;return i.supportsBinary=Q&&!r,i}return e(n,t),n.prototype.request=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return i(t,{xd:this.xd},this.opts),new J(Z,this.uri(),t)},n}(G);function Z(t){var n=t.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!n||F))return new XMLHttpRequest}catch(t){}if(!n)try{return new(R[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(t){}}var _="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase(),tt=function(t){function i(){return t.apply(this,arguments)||this}e(i,t);var r=i.prototype;return r.doOpen=function(){var t=this.uri(),n=this.opts.protocols,i=_?{}:D(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(i.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,n,i)}catch(t){return this.emitReserved("error",t)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()},r.addEventListeners=function(){var t=this;this.ws.onopen=function(){t.opts.autoUnref&&t.ws.S.unref(),t.onOpen()},this.ws.onclose=function(n){return t.onClose({description:"websocket connection closed",context:n})},this.ws.onmessage=function(n){return t.onData(n.data)},this.ws.onerror=function(n){return t.onError("websocket error",n)}},r.write=function(t){var n=this;this.writable=!1;for(var i=function(){var i=t[r],e=r===t.length-1;b(i,n.supportsBinary,(function(t){try{n.doWrite(i,t)}catch(t){}e&&L((function(){n.writable=!0,n.emitReserved("drain")}),n.setTimeoutFn)}))},r=0;r<t.length;r++)i()},r.doClose=function(){void 0!==this.ws&&(this.ws.onerror=function(){},this.ws.close(),this.ws=null)},r.uri=function(){var t=this.opts.secure?"wss":"ws",n=this.query||{};return this.opts.timestampRequests&&(n[this.opts.timestampParam]=q()),this.supportsBinary||(n.b64=1),this.createUri(t,n)},n(i,[{key:"name",get:function(){return"websocket"}}])}(V),nt=R.WebSocket||R.MozWebSocket,it=function(t){function n(){return t.apply(this,arguments)||this}e(n,t);var i=n.prototype;return i.createSocket=function(t,n,i){return _?new nt(t,n,i):n?new nt(t,n):new nt(t)},i.doWrite=function(t,n){this.ws.send(n)},n}(tt),rt=function(t){function i(){return t.apply(this,arguments)||this}e(i,t);var r=i.prototype;return r.doOpen=function(){var t=this;try{this.M=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this.M.closed.then((function(){t.onClose()})).catch((function(n){t.onError("webtransport error",n)})),this.M.ready.then((function(){t.M.createBidirectionalStream().then((function(n){var i=function(t,n){j||(j=new TextDecoder);var i=[],r=0,e=-1,o=!1;return new TransformStream({transform:function(s,u){for(i.push(s);;){if(0===r){if(M(i)<1)break;var f=x(i,1);o=!(128&~f[0]),e=127&f[0],r=e<126?3:126===e?1:2}else if(1===r){if(M(i)<2)break;var h=x(i,2);e=new DataView(h.buffer,h.byteOffset,h.length).getUint16(0),r=3}else if(2===r){if(M(i)<8)break;var c=x(i,8),a=new DataView(c.buffer,c.byteOffset,c.length),p=a.getUint32(0);if(p>Math.pow(2,21)-1){u.enqueue(l);break}e=p*Math.pow(2,32)+a.getUint32(4),r=3}else{if(M(i)<e)break;var v=x(i,e);u.enqueue(E(o?v:j.decode(v),n)),r=0}if(0===e||e>t){u.enqueue(l);break}}}})}(Number.MAX_SAFE_INTEGER,t.socket.binaryType),r=n.readable.pipeThrough(i).getReader(),e=S();e.readable.pipeTo(n.writable),t.C=e.writable.getWriter();!function n(){r.read().then((function(i){var r=i.done,e=i.value;r||(t.onPacket(e),n())})).catch((function(t){}))}();var o={type:"open"};t.query.sid&&(o.data='{"sid":"'.concat(t.query.sid,'"}')),t.C.write(o).then((function(){return t.onOpen()}))}))}))},r.write=function(t){var n=this;this.writable=!1;for(var i=function(){var i=t[r],e=r===t.length-1;n.C.write(i).then((function(){e&&L((function(){n.writable=!0,n.emitReserved("drain")}),n.setTimeoutFn)}))},r=0;r<t.length;r++)i()},r.doClose=function(){var t;null===(t=this.M)||void 0===t||t.close()},n(i,[{key:"name",get:function(){return"webtransport"}}])}(V),et={websocket:it,webtransport:rt,polling:Y},ot=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,st=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ut(t){if(t.length>8e3)throw"URI too long";var n=t,i=t.indexOf("["),r=t.indexOf("]");-1!=i&&-1!=r&&(t=t.substring(0,i)+t.substring(i,r).replace(/:/g,";")+t.substring(r,t.length));for(var e,o,s=ot.exec(t||""),u={},f=14;f--;)u[st[f]]=s[f]||"";return-1!=i&&-1!=r&&(u.source=n,u.host=u.host.substring(1,u.host.length-1).replace(/;/g,":"),u.authority=u.authority.replace("[","").replace("]","").replace(/;/g,":"),u.ipv6uri=!0),u.pathNames=function(t,n){var i=/\/{2,9}/g,r=n.replace(i,"/").split("/");"/"!=n.slice(0,1)&&0!==n.length||r.splice(0,1);"/"==n.slice(-1)&&r.splice(r.length-1,1);return r}(0,u.path),u.queryKey=(e=u.query,o={},e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,(function(t,n,i){n&&(o[n]=i)})),o),u}var ft="function"==typeof addEventListener&&"function"==typeof removeEventListener,ht=[];ft&&addEventListener("offline",(function(){ht.forEach((function(t){return t()}))}),!1);var ct=function(t){function n(n,r){var e;if((e=t.call(this)||this).binaryType="arraybuffer",e.writeBuffer=[],e.L=0,e.R=-1,e.D=-1,e.P=-1,e.I=1/0,n&&"object"===f(n)&&(r=n,n=null),n){var o=ut(n);r.hostname=o.host,r.secure="https"===o.protocol||"wss"===o.protocol,r.port=o.port,o.query&&(r.query=o.query)}else r.host&&(r.hostname=ut(r.host).host);return $(e,r),e.secure=null!=r.secure?r.secure:"undefined"!=typeof location&&"https:"===location.protocol,r.hostname&&!r.port&&(r.port=e.secure?"443":"80"),e.hostname=r.hostname||("undefined"!=typeof location?location.hostname:"localhost"),e.port=r.port||("undefined"!=typeof location&&location.port?location.port:e.secure?"443":"80"),e.transports=[],e.$={},r.transports.forEach((function(t){var n=t.prototype.name;e.transports.push(n),e.$[n]=t})),e.opts=i({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},r),e.opts.path=e.opts.path.replace(/\/$/,"")+(e.opts.addTrailingSlash?"/":""),"string"==typeof e.opts.query&&(e.opts.query=function(t){for(var n={},i=t.split("&"),r=0,e=i.length;r<e;r++){var o=i[r].split("=");n[decodeURIComponent(o[0])]=decodeURIComponent(o[1])}return n}(e.opts.query)),ft&&(e.opts.closeOnBeforeunload&&(e.q=function(){e.transport&&(e.transport.removeAllListeners(),e.transport.close())},addEventListener("beforeunload",e.q,!1)),"localhost"!==e.hostname&&(e.N=function(){e.V("transport close",{description:"network connection lost"})},ht.push(e.N))),e.opts.withCredentials&&(e.X=void 0),e.H(),e}e(n,t);var r=n.prototype;return r.createTransport=function(t){var n=i({},this.opts.query);n.EIO=4,n.transport=t,this.id&&(n.sid=this.id);var r=i({},this.opts,{query:n,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this.$[t](r)},r.H=function(){var t=this;if(0!==this.transports.length){var i=this.opts.rememberUpgrade&&n.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";var r=this.createTransport(i);r.open(),this.setTransport(r)}else this.setTimeoutFn((function(){t.emitReserved("error","No transports available")}),0)},r.setTransport=function(t){var n=this;this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this.F.bind(this)).on("packet",this.G.bind(this)).on("error",this.O.bind(this)).on("close",(function(t){return n.V("transport close",t)}))},r.onOpen=function(){this.readyState="open",n.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()},r.G=function(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this.J("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this.W();break;case"error":var n=new Error("server error");n.code=t.data,this.O(n);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data)}},r.onHandshake=function(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this.R=t.pingInterval,this.D=t.pingTimeout,this.P=t.maxPayload,this.onOpen(),"closed"!==this.readyState&&this.W()},r.W=function(){var t=this;this.clearTimeoutFn(this.K);var n=this.R+this.D;this.I=Date.now()+n,this.K=this.setTimeoutFn((function(){t.V("ping timeout")}),n),this.opts.autoUnref&&this.K.unref()},r.F=function(){this.writeBuffer.splice(0,this.L),this.L=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()},r.flush=function(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){var t=this.Y();this.transport.send(t),this.L=t.length,this.emitReserved("flush")}},r.Y=function(){if(!(this.P&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;for(var t,n=1,i=0;i<this.writeBuffer.length;i++){var r=this.writeBuffer[i].data;if(r&&(n+="string"==typeof(t=r)?function(t){for(var n=0,i=0,r=0,e=t.length;r<e;r++)(n=t.charCodeAt(r))<128?i+=1:n<2048?i+=2:n<55296||n>=57344?i+=3:(r++,i+=4);return i}(t):Math.ceil(1.33*(t.byteLength||t.size))),i>0&&n>this.P)return this.writeBuffer.slice(0,i);n+=2}return this.writeBuffer},r.Z=function(){var t=this;if(!this.I)return!0;var n=Date.now()>this.I;return n&&(this.I=0,L((function(){t.V("ping timeout")}),this.setTimeoutFn)),n},r.write=function(t,n,i){return this.J("message",t,n,i),this},r.send=function(t,n,i){return this.J("message",t,n,i),this},r.J=function(t,n,i,r){if("function"==typeof n&&(r=n,n=void 0),"function"==typeof i&&(r=i,i=null),"closing"!==this.readyState&&"closed"!==this.readyState){(i=i||{}).compress=!1!==i.compress;var e={type:t,data:n,options:i};this.emitReserved("packetCreate",e),this.writeBuffer.push(e),r&&this.once("flush",r),this.flush()}},r.close=function(){var t=this,n=function(){t.V("forced close"),t.transport.close()},i=function i(){t.off("upgrade",i),t.off("upgradeError",i),n()},r=function(){t.once("upgrade",i),t.once("upgradeError",i)};return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",this.writeBuffer.length?this.once("drain",(function(){t.upgrading?r():n()})):this.upgrading?r():n()),this},r.O=function(t){if(n.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this.H();this.emitReserved("error",t),this.V("transport error",t)},r.V=function(t,n){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this.K),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),ft&&(this.q&&removeEventListener("beforeunload",this.q,!1),this.N)){var i=ht.indexOf(this.N);-1!==i&&ht.splice(i,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,n),this.writeBuffer=[],this.L=0}},n}(C);ct.protocol=4;var at=function(t){function n(){var n;return(n=t.apply(this,arguments)||this)._=[],n}e(n,t);var i=n.prototype;return i.onOpen=function(){if(t.prototype.onOpen.call(this),"open"===this.readyState&&this.opts.upgrade)for(var n=0;n<this._.length;n++)this.tt(this._[n])},i.tt=function(t){var n=this,i=this.createTransport(t),r=!1;ct.priorWebsocketSuccess=!1;var e=function(){r||(i.send([{type:"ping",data:"probe"}]),i.once("packet",(function(t){if(!r)if("pong"===t.type&&"probe"===t.data){if(n.upgrading=!0,n.emitReserved("upgrading",i),!i)return;ct.priorWebsocketSuccess="websocket"===i.name,n.transport.pause((function(){r||"closed"!==n.readyState&&(c(),n.setTransport(i),i.send([{type:"upgrade"}]),n.emitReserved("upgrade",i),i=null,n.upgrading=!1,n.flush())}))}else{var e=new Error("probe error");e.transport=i.name,n.emitReserved("upgradeError",e)}})))};function o(){r||(r=!0,c(),i.close(),i=null)}var s=function(t){var r=new Error("probe error: "+t);r.transport=i.name,o(),n.emitReserved("upgradeError",r)};function u(){s("transport closed")}function f(){s("socket closed")}function h(t){i&&t.name!==i.name&&o()}var c=function(){i.removeListener("open",e),i.removeListener("error",s),i.removeListener("close",u),n.off("close",f),n.off("upgrading",h)};i.once("open",e),i.once("error",s),i.once("close",u),this.once("close",f),this.once("upgrading",h),-1!==this._.indexOf("webtransport")&&"webtransport"!==t?this.setTimeoutFn((function(){r||i.open()}),200):i.open()},i.onHandshake=function(n){this._=this.nt(n.upgrades),t.prototype.onHandshake.call(this,n)},i.nt=function(t){for(var n=[],i=0;i<t.length;i++)~this.transports.indexOf(t[i])&&n.push(t[i]);return n},n}(ct),pt=function(t){function n(n){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="object"===f(n)?n:i;return(!r.transports||r.transports&&"string"==typeof r.transports[0])&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map((function(t){return et[t]})).filter((function(t){return!!t}))),t.call(this,n,r)||this}return e(n,t),n}(at);return function(t,n){return new pt(t,n)}}));
//# sourceMappingURL=engine.io.min.js.map
