// src/navigation/types.ts

// Re-export MainTabParamList for use in other parts of your app if needed
export type { MainTabParamList } from './MainTabNavigator'; // <--- IMPORTANT: Re-export MainTabParamList

// This defines the screens and their parameters for your primary app stack
export type AppStackParamList = {
    // This now refers to your Tab Navigator
    MainTabs: undefined; // Or you can pass parameters if your tabs need them

    // These screens open *above* the tabs
    JobDetail: { jobId: string };
    Application: { jobId: string };
    MyApplications: undefined;
    EditProfile: undefined;
    PasswordChange: undefined;
    CategoryScreen: undefined;
    PrivacySettings: undefined;
    HelpSupport: undefined;
    About: undefined;

    // Chat screens
    ChatList: undefined;
    ChatDetail: { conversationId: string };

    // If you have any other screens that are part of AppStack but NOT in tabs, add them here
};

// This defines the screens and their parameters for your authentication stack
export type AuthStackParamList = {
    Welcome: undefined;
    Login: { role?: 'jobSeeker' | 'employer'; isGuest?: boolean };
    Register: { role?: 'jobSeeker' | 'employer' };
    ForgotPassword: undefined;
    ResetPassword: { token: string };
};

// This defines the parameters for your Root Navigator
export type RootStackParamList = {
    Auth: undefined;
    App: undefined;  // 'App' now contains your AppStackNavigator, which in turn contains MainTabs
};

// This is useful for `useNavigation` hook
declare global {
    namespace ReactNavigation {
        interface RootParamList extends RootStackParamList { }
    }
}