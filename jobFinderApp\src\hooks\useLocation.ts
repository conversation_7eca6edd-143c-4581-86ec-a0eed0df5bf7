// hooks/useLocation.ts
import { useState, useEffect } from 'react';
import { PermissionsAndroid, Platform } from 'react-native';
import Geolocation from 'react-native-geolocation-service';

interface Location {
    latitude: number;
    longitude: number;
}

const useLocation = () => {
    const [location, setLocation] = useState<Location | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState<boolean>(true);

    useEffect(() => {
        const requestLocationPermission = async () => {
            if (Platform.OS === 'android') {
                try {
                    const granted = await PermissionsAndroid.request(
                        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                        {
                            title: "Location Permission",
                            message: "This app needs access to your location to find nearby jobs.",
                            buttonNeutral: "Ask Me Later",
                            buttonNegative: "Cancel",
                            buttonPositive: "OK"
                        }
                    );
                    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
                        console.log("Location permission granted");
                        fetchCurrentLocation(true); // Start with high accuracy
                    } else {
                        setError("Location permission denied.");
                        setLoading(false);
                        console.log("Location permission denied");
                    }
                } catch (err) {
                    console.warn(err);
                    setError("Error requesting location permission.");
                    setLoading(false);
                }
            } else { // For iOS, permissions are handled differently (usually through Info.plist and automatic prompts)
                fetchCurrentLocation(true); // Start with high accuracy
            }
        };

        const fetchCurrentLocation = (highAccuracy = false) => {
            const options = {
                enableHighAccuracy: highAccuracy,
                timeout: highAccuracy ? 20000 : 30000,
                maximumAge: 300000 // Accept cached location up to 5 minutes old
            };

            Geolocation.getCurrentPosition(
                (position) => {
                    setLocation({
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                    });
                    setLoading(false);
                    setError(null);
                    console.log("Location obtained successfully:", position.coords);
                },
                (geoError) => {
                    console.error("Geolocation error:", geoError);

                    // If high accuracy failed, try with low accuracy
                    if (highAccuracy && geoError.code === 3) {
                        console.log("High accuracy timed out, trying with network location...");
                        fetchCurrentLocation(false);
                        return;
                    }

                    // Set appropriate error message based on error code
                    let errorMessage = "Failed to get location.";
                    switch (geoError.code) {
                        case 1:
                            errorMessage = "Location access denied. Please enable location permissions.";
                            break;
                        case 2:
                            errorMessage = "Location unavailable. Please check your GPS settings.";
                            break;
                        case 3:
                            errorMessage = "Location request timed out. Using default location.";
                            // Set a default location - you can customize this based on your app's target region
                            setLocation({
                                latitude: 40.7128, // New York City coordinates as fallback
                                longitude: -74.0060
                            });
                            console.log("Using default location due to timeout");
                            break;
                    }

                    setError(errorMessage);
                    setLoading(false);
                },
                options
            );
        };

        requestLocationPermission();
    }, []);

    return { location, error, loading };
};

export default useLocation;