// backend/src/socket/socketHandler.ts

import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { authenticateSocket, AuthenticatedSocket, getUserId, requireAuth } from './socketAuth';
import Conversation from '../models/Conversation';
import Message from '../models/Message';
import { Types } from 'mongoose';

// Store active user connections
const activeUsers = new Map<string, string>(); // userId -> socketId
const userSockets = new Map<string, AuthenticatedSocket>(); // socketId -> socket

export const initializeSocket = (server: HTTPServer): SocketIOServer => {
    const io = new SocketIOServer(server, {
        cors: {
            origin: "*", // Configure this properly for production
            methods: ["GET", "POST"]
        }
    });

    // Authentication middleware
    io.use(authenticateSocket);

    io.on('connection', (socket: AuthenticatedSocket) => {
        console.log(`User connected: ${socket.user?.email} (${socket.id})`);

        const userId = getUserId(socket);
        if (userId) {
            // Store user connection
            activeUsers.set(userId, socket.id);
            userSockets.set(socket.id, socket);

            // Join user to their personal room
            socket.join(`user:${userId}`);

            // Emit user online status
            socket.broadcast.emit('user:online', { userId });
        }

        // Handle joining conversation rooms
        socket.on('join:conversation', async (data: { conversationId: string }) => {
            try {
                if (!requireAuth(socket)) return;

                const { conversationId } = data;
                const userId = getUserId(socket);

                // Verify user is part of this conversation
                const conversation = await Conversation.findOne({
                    _id: conversationId,
                    $or: [
                        { jobSeeker: userId },
                        { employer: userId }
                    ]
                });

                if (!conversation) {
                    socket.emit('error', { message: 'Conversation not found or access denied' });
                    return;
                }

                // Join conversation room
                socket.join(`conversation:${conversationId}`);
                socket.emit('joined:conversation', { conversationId });

                console.log(`User ${userId} joined conversation ${conversationId}`);
            } catch (error) {
                console.error('Error joining conversation:', error);
                socket.emit('error', { message: 'Failed to join conversation' });
            }
        });

        // Handle leaving conversation rooms
        socket.on('leave:conversation', (data: { conversationId: string }) => {
            const { conversationId } = data;
            socket.leave(`conversation:${conversationId}`);
            socket.emit('left:conversation', { conversationId });
        });

        // Handle sending messages
        socket.on('send:message', async (data: {
            conversationId: string;
            content: string;
            messageType?: 'text' | 'image' | 'file';
        }) => {
            try {
                if (!requireAuth(socket)) return;

                const { conversationId, content, messageType = 'text' } = data;
                const userId = getUserId(socket);

                if (!userId) {
                    socket.emit('error', { message: 'User not authenticated' });
                    return;
                }

                // Verify conversation exists and user has access
                const conversation = await Conversation.findOne({
                    _id: conversationId,
                    $or: [
                        { jobSeeker: userId },
                        { employer: userId }
                    ]
                }).populate('jobSeeker employer', 'firstName lastName email role');

                if (!conversation) {
                    socket.emit('error', { message: 'Conversation not found or access denied' });
                    return;
                }

                // Determine receiver
                const receiverId = conversation.jobSeeker._id.toString() === userId 
                    ? conversation.employer._id.toString() 
                    : conversation.jobSeeker._id.toString();

                // Create new message
                const newMessage = new Message({
                    conversation: conversationId,
                    sender: userId,
                    receiver: receiverId,
                    content: content.trim(),
                    messageType
                });

                await newMessage.save();

                // Populate sender details
                await newMessage.populate('sender', 'firstName lastName email role');

                // Update conversation unread count
                const updateField = conversation.jobSeeker._id.toString() === userId
                    ? 'unreadCount.employer'
                    : 'unreadCount.jobSeeker';

                await Conversation.findByIdAndUpdate(conversationId, {
                    $inc: { [updateField]: 1 }
                });

                // Emit message to conversation room
                io.to(`conversation:${conversationId}`).emit('new:message', {
                    message: newMessage,
                    conversationId
                });

                // Emit to receiver's personal room for notifications
                io.to(`user:${receiverId}`).emit('message:notification', {
                    conversationId,
                    message: newMessage,
                    sender: newMessage.sender
                });

                console.log(`Message sent in conversation ${conversationId} by user ${userId}`);
            } catch (error) {
                console.error('Error sending message:', error);
                socket.emit('error', { message: 'Failed to send message' });
            }
        });

        // Handle marking messages as read
        socket.on('mark:read', async (data: { conversationId: string, messageIds?: string[] }) => {
            try {
                if (!requireAuth(socket)) return;

                const { conversationId, messageIds } = data;
                const userId = getUserId(socket);

                if (!userId) return;

                // Verify conversation access
                const conversation = await Conversation.findOne({
                    _id: conversationId,
                    $or: [
                        { jobSeeker: userId },
                        { employer: userId }
                    ]
                });

                if (!conversation) return;

                // Mark messages as read
                const query: any = {
                    conversation: conversationId,
                    receiver: userId,
                    isRead: false
                };

                if (messageIds && messageIds.length > 0) {
                    query._id = { $in: messageIds };
                }

                await Message.updateMany(query, {
                    $set: { isRead: true },
                    $push: {
                        readBy: {
                            user: userId,
                            readAt: new Date()
                        }
                    }
                });

                // Reset unread count for this user
                const updateField = conversation.jobSeeker._id.toString() === userId 
                    ? 'unreadCount.jobSeeker' 
                    : 'unreadCount.employer';

                await Conversation.findByIdAndUpdate(conversationId, {
                    $set: { [updateField]: 0 }
                });

                // Emit read receipt to conversation
                io.to(`conversation:${conversationId}`).emit('messages:read', {
                    conversationId,
                    readBy: userId,
                    messageIds: messageIds || 'all'
                });

                console.log(`Messages marked as read in conversation ${conversationId} by user ${userId}`);
            } catch (error) {
                console.error('Error marking messages as read:', error);
            }
        });

        // Handle typing indicators
        socket.on('typing:start', (data: { conversationId: string }) => {
            const userId = getUserId(socket);
            if (userId) {
                socket.to(`conversation:${data.conversationId}`).emit('user:typing', {
                    userId,
                    conversationId: data.conversationId
                });
            }
        });

        socket.on('typing:stop', (data: { conversationId: string }) => {
            const userId = getUserId(socket);
            if (userId) {
                socket.to(`conversation:${data.conversationId}`).emit('user:stopped_typing', {
                    userId,
                    conversationId: data.conversationId
                });
            }
        });

        // Handle disconnection
        socket.on('disconnect', () => {
            const userId = getUserId(socket);
            if (userId) {
                activeUsers.delete(userId);
                userSockets.delete(socket.id);

                // Emit user offline status
                socket.broadcast.emit('user:offline', { userId });
                console.log(`User disconnected: ${socket.user?.email} (${socket.id})`);
            }
        });
    });

    return io;
};

export const getActiveUsers = (): Map<string, string> => {
    return activeUsers;
};

export const isUserOnline = (userId: string): boolean => {
    return activeUsers.has(userId);
};
