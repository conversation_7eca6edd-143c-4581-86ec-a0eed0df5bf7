// src/navigation/MainTabNavigator.tsx

import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Feather from 'react-native-vector-icons/Feather';
import { StyleSheet, View, Text } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { useTheme } from '../contexts/ThemeContext';

import HomeScreen from '../screens/jobs/HomeScreen';
import SearchScreen from '../screens/jobs/SearchScreen';
import ProfileScreen from '../screens/profile/ProfileScreen';
import ChatListScreen from '../screens/chat/ChatListScreen';

export type MainTabParamList = {
    Home: undefined;
    Search: undefined;
    Messages: undefined;
    Profile: undefined;
};

const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabNavigator: React.FC = () => {
    const isGuest = useSelector((state: RootState) => state.auth.isGuest);
    const { theme } = useTheme();
    const { totalUnreadCount } = useSelector((state: RootState) => state.chat);

    return (
        <Tab.Navigator
            screenOptions={({ route }) => ({
                headerShown: false,
                lazy: false,
                unmountOnBlur: false,
                tabBarIcon: ({ focused, color, size }) => {
                    let iconName = '';
                    if (route.name === 'Home') iconName = 'home';
                    else if (route.name === 'Search') iconName = 'search';
                    else if (route.name === 'Messages') iconName = 'message-circle';
                    else if (route.name === 'Profile') iconName = 'user';

                    if (route.name === 'Home' && focused) {
                        return (
                            <View style={[tabStyles.homeActiveBg, { backgroundColor: theme.primary + '15' }]}>
                                <Feather name={iconName} size={size} color={theme.primary} />
                            </View>
                        );
                    }

                    if (route.name === 'Messages') {
                        return (
                            <View style={tabStyles.messageIconContainer}>
                                <Feather name={iconName} size={size} color={focused ? theme.primary : theme.textSecondary} />
                                {totalUnreadCount > 0 && (
                                    <View style={[tabStyles.unreadBadge, { backgroundColor: theme.error || '#FF3B30' }]}>
                                        <Text style={tabStyles.unreadText}>
                                            {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
                                        </Text>
                                    </View>
                                )}
                            </View>
                        );
                    }

                    return <Feather name={iconName} size={size} color={focused ? theme.primary : theme.textSecondary} />;
                },
                tabBarActiveTintColor: theme.primary,
                tabBarInactiveTintColor: theme.textSecondary,
                tabBarLabelStyle: { fontWeight: '600', fontSize: 12, color: theme.textSecondary },
                tabBarStyle: {
                    height: 70,
                    paddingBottom: 10,
                    paddingTop: 10,
                    backgroundColor: theme.surface,
                    borderTopColor: theme.border,
                    borderTopWidth: 0.5,
                    shadowColor: theme.shadow,
                    shadowOffset: { width: 0, height: -2 },
                    shadowOpacity: 0.1,
                    shadowRadius: 8,
                    elevation: 8,
                },
            })}
        >
            <Tab.Screen name="Home" component={HomeScreen} />
            <Tab.Screen name="Search" component={SearchScreen} />
            <Tab.Screen name="Messages" component={ChatListScreen} />
            <Tab.Screen name="Profile" component={ProfileScreen} />
        </Tab.Navigator>
    );
};

const tabStyles = StyleSheet.create({
    homeActiveBg: {
        borderRadius: 20,
        paddingHorizontal: 12,
        paddingVertical: 6,
        alignItems: 'center',
        justifyContent: 'center',
    },
    messageIconContainer: {
        position: 'relative',
    },
    unreadBadge: {
        position: 'absolute',
        top: -6,
        right: -8,
        minWidth: 18,
        height: 18,
        borderRadius: 9,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 4,
    },
    unreadText: {
        color: '#FFFFFF',
        fontSize: 10,
        fontWeight: '600',
    },
});

export default MainTabNavigator;