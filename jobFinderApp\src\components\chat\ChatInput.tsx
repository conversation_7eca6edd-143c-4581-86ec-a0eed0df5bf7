// jobFinderApp/src/components/chat/ChatInput.tsx

import React, { useState, useRef, useEffect } from 'react';
import {
    View,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    Keyboard,
    Platform,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';

interface ChatInputProps {
    onSendMessage: (message: string) => void;
    onTypingStart?: () => void;
    onTypingStop?: () => void;
    placeholder?: string;
    disabled?: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({
    onSendMessage,
    onTypingStart,
    onTypingStop,
    placeholder = 'Type a message...',
    disabled = false
}) => {
    const { theme } = useTheme();
    const [message, setMessage] = useState('');
    const [isTyping, setIsTyping] = useState(false);
    const inputRef = useRef<TextInput>(null);
    const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Handle typing indicators
    useEffect(() => {
        if (message.trim().length > 0 && !isTyping) {
            setIsTyping(true);
            onTypingStart?.();
        }

        // Clear existing timeout
        if (typingTimeoutRef.current) {
            clearTimeout(typingTimeoutRef.current);
        }

        // Set new timeout to stop typing indicator
        typingTimeoutRef.current = setTimeout(() => {
            if (isTyping) {
                setIsTyping(false);
                onTypingStop?.();
            }
        }, 1000);

        return () => {
            if (typingTimeoutRef.current) {
                clearTimeout(typingTimeoutRef.current);
            }
        };
    }, [message, isTyping, onTypingStart, onTypingStop]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (typingTimeoutRef.current) {
                clearTimeout(typingTimeoutRef.current);
            }
            if (isTyping) {
                onTypingStop?.();
            }
        };
    }, [isTyping, onTypingStop]);

    const handleSend = () => {
        const trimmedMessage = message.trim();
        if (trimmedMessage.length > 0 && !disabled) {
            onSendMessage(trimmedMessage);
            setMessage('');
            
            // Stop typing indicator immediately
            if (isTyping) {
                setIsTyping(false);
                onTypingStop?.();
            }
            
            // Clear timeout
            if (typingTimeoutRef.current) {
                clearTimeout(typingTimeoutRef.current);
            }
        }
    };

    const handleTextChange = (text: string) => {
        setMessage(text);
    };

    const canSend = message.trim().length > 0 && !disabled;

    return (
        <View style={[styles.container, { backgroundColor: theme.background, borderTopColor: theme.border }]}>
            <View style={[styles.inputContainer, { backgroundColor: theme.surface, borderColor: theme.border }]}>
                <TextInput
                    ref={inputRef}
                    style={[styles.textInput, { color: theme.text }]}
                    value={message}
                    onChangeText={handleTextChange}
                    placeholder={placeholder}
                    placeholderTextColor={theme.textSecondary}
                    multiline
                    maxLength={2000}
                    editable={!disabled}
                    returnKeyType="send"
                    onSubmitEditing={handleSend}
                    blurOnSubmit={false}
                />
                
                <TouchableOpacity
                    style={[
                        styles.sendButton,
                        { 
                            backgroundColor: canSend ? theme.primary : theme.textSecondary,
                            opacity: canSend ? 1 : 0.5
                        }
                    ]}
                    onPress={handleSend}
                    disabled={!canSend}
                    activeOpacity={0.7}
                >
                    <Ionicons 
                        name="send" 
                        size={18} 
                        color="#FFFFFF" 
                    />
                </TouchableOpacity>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderTopWidth: 1,
        ...Platform.select({
            ios: {
                paddingBottom: 34, // Account for home indicator on iOS
            },
            android: {
                paddingBottom: 12,
            },
        }),
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'flex-end',
        borderRadius: 24,
        borderWidth: 1,
        paddingHorizontal: 16,
        paddingVertical: 8,
        minHeight: 48,
    },
    textInput: {
        flex: 1,
        fontSize: 16,
        lineHeight: 20,
        maxHeight: 100,
        paddingVertical: 8,
        paddingRight: 12,
        textAlignVertical: 'center',
    },
    sendButton: {
        width: 32,
        height: 32,
        borderRadius: 16,
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: 8,
    },
});

export default ChatInput;
