// jobFinderApp/src/screens/chat/ChatListScreen.tsx

import React, { useEffect, useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    FlatList,
    TouchableOpacity,
    ActivityIndicator,
    RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useDispatch, useSelector } from 'react-redux';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useTheme } from '../../contexts/ThemeContext';
import { AppDispatch, RootState } from '../../redux/store';
import { fetchConversations, setCurrentConversation } from '../../redux/chat/chatSlice';
import { Conversation } from '../../redux/chat/chatSlice';
import { AppStackParamList } from '../../types/navigation';

type ChatListNavigationProp = NativeStackNavigationProp<AppStackParamList, 'ChatList'>;

const ChatListScreen: React.FC = () => {
    const { theme } = useTheme();
    const navigation = useNavigation<ChatListNavigationProp>();
    const dispatch = useDispatch<AppDispatch>();
    
    const { conversations, isLoading, error } = useSelector((state: RootState) => state.chat);
    const { user } = useSelector((state: RootState) => state.auth);
    const [refreshing, setRefreshing] = useState(false);

    useEffect(() => {
        loadConversations();
    }, []);

    const loadConversations = async () => {
        try {
            await dispatch(fetchConversations());
        } catch (error) {
            console.error('Error loading conversations:', error);
        }
    };

    const onRefresh = async () => {
        setRefreshing(true);
        await loadConversations();
        setRefreshing(false);
    };

    const handleConversationPress = (conversation: Conversation) => {
        dispatch(setCurrentConversation(conversation));
        navigation.navigate('ChatDetail', { conversationId: conversation._id });
    };

    const getOtherUser = (conversation: Conversation) => {
        return user?.role === 'jobseeker' ? conversation.employer : conversation.jobSeeker;
    };

    const getUnreadCount = (conversation: Conversation) => {
        return user?.role === 'jobseeker' 
            ? conversation.unreadCount.jobSeeker 
            : conversation.unreadCount.employer;
    };

    const formatLastMessageTime = (dateString: string): string => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

        if (diffInHours < 1) {
            const diffInMinutes = Math.floor(diffInHours * 60);
            return diffInMinutes < 1 ? 'now' : `${diffInMinutes}m`;
        } else if (diffInHours < 24) {
            return `${Math.floor(diffInHours)}h`;
        } else {
            const diffInDays = Math.floor(diffInHours / 24);
            return diffInDays === 1 ? '1d' : `${diffInDays}d`;
        }
    };

    const renderConversationItem = ({ item }: { item: Conversation }) => {
        const otherUser = getOtherUser(item);
        const unreadCount = getUnreadCount(item);
        const hasUnread = unreadCount > 0;

        return (
            <TouchableOpacity
                style={[styles.conversationItem, { backgroundColor: theme.surface, borderBottomColor: theme.border }]}
                onPress={() => handleConversationPress(item)}
                activeOpacity={0.7}
            >
                <View style={styles.conversationContent}>
                    {/* Avatar */}
                    <View style={[styles.avatar, { backgroundColor: theme.primary + '20' }]}>
                        <Text style={[styles.avatarText, { color: theme.primary }]}>
                            {otherUser.firstName[0]}{otherUser.lastName[0]}
                        </Text>
                    </View>

                    {/* Conversation Info */}
                    <View style={styles.conversationInfo}>
                        <View style={styles.conversationHeader}>
                            <Text style={[styles.userName, { color: theme.text }]} numberOfLines={1}>
                                {otherUser.firstName} {otherUser.lastName}
                            </Text>
                            {item.lastMessage && (
                                <Text style={[styles.timestamp, { color: theme.textSecondary }]}>
                                    {formatLastMessageTime(item.lastMessage.timestamp)}
                                </Text>
                            )}
                        </View>

                        <View style={styles.conversationSubHeader}>
                            <Text style={[styles.jobTitle, { color: theme.textSecondary }]} numberOfLines={1}>
                                {item.job.title} at {item.job.company}
                            </Text>
                        </View>

                        {item.lastMessage && (
                            <View style={styles.lastMessageContainer}>
                                <Text 
                                    style={[
                                        styles.lastMessage, 
                                        { 
                                            color: hasUnread ? theme.text : theme.textSecondary,
                                            fontWeight: hasUnread ? '600' : '400'
                                        }
                                    ]} 
                                    numberOfLines={1}
                                >
                                    {item.lastMessage.content}
                                </Text>
                                {hasUnread && (
                                    <View style={[styles.unreadBadge, { backgroundColor: theme.primary }]}>
                                        <Text style={styles.unreadCount}>
                                            {unreadCount > 99 ? '99+' : unreadCount}
                                        </Text>
                                    </View>
                                )}
                            </View>
                        )}
                    </View>
                </View>
            </TouchableOpacity>
        );
    };

    const renderEmptyState = () => (
        <View style={styles.emptyState}>
            <Ionicons name="chatbubbles-outline" size={64} color={theme.textSecondary} />
            <Text style={[styles.emptyStateTitle, { color: theme.text }]}>No Conversations Yet</Text>
            <Text style={[styles.emptyStateText, { color: theme.textSecondary }]}>
                Start messaging with employers or job seekers after applying to jobs.
            </Text>
        </View>
    );

    if (isLoading && conversations.length === 0) {
        return (
            <View style={[styles.container, styles.centered, { backgroundColor: theme.background }]}>
                <ActivityIndicator size="large" color={theme.primary} />
                <Text style={[styles.loadingText, { color: theme.text }]}>Loading conversations...</Text>
            </View>
        );
    }

    return (
        <View style={[styles.container, { backgroundColor: theme.background }]}>
            <FlatList
                data={conversations}
                renderItem={renderConversationItem}
                keyExtractor={(item) => item._id}
                ListEmptyComponent={renderEmptyState}
                refreshControl={
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                }
                contentContainerStyle={conversations.length === 0 ? styles.emptyContainer : undefined}
                showsVerticalScrollIndicator={false}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    centered: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
    },
    conversationItem: {
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
    },
    conversationContent: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    avatar: {
        width: 50,
        height: 50,
        borderRadius: 25,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    avatarText: {
        fontSize: 18,
        fontWeight: '600',
    },
    conversationInfo: {
        flex: 1,
    },
    conversationHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 2,
    },
    userName: {
        fontSize: 16,
        fontWeight: '600',
        flex: 1,
    },
    timestamp: {
        fontSize: 12,
        marginLeft: 8,
    },
    conversationSubHeader: {
        marginBottom: 4,
    },
    jobTitle: {
        fontSize: 13,
        fontStyle: 'italic',
    },
    lastMessageContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    lastMessage: {
        fontSize: 14,
        flex: 1,
    },
    unreadBadge: {
        minWidth: 20,
        height: 20,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 6,
        marginLeft: 8,
    },
    unreadCount: {
        color: '#FFFFFF',
        fontSize: 12,
        fontWeight: '600',
    },
    emptyState: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 32,
    },
    emptyContainer: {
        flex: 1,
    },
    emptyStateTitle: {
        fontSize: 20,
        fontWeight: '600',
        marginTop: 16,
        marginBottom: 8,
    },
    emptyStateText: {
        fontSize: 16,
        textAlign: 'center',
        lineHeight: 24,
    },
});

export default ChatListScreen;
