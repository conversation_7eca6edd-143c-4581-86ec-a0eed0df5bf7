// src/types/navigation.ts

import { NavigatorScreenParams } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack'; // Assuming you use @react-navigation/stack
import { MaterialTopTabNavigationProp } from '@react-navigation/material-top-tabs'; // Or @react-navigation/bottom-tabs if you use bottom tabs

// Define parameters for your main App tabs
export type AppTabParamList = {
    Home: undefined;
    Search: undefined;
    Saved: undefined; // <-- 'Saved' is a direct screen in THIS tab navigator
    Messages: undefined; // <-- Chat tab
    Profile: undefined;
    // Add other tab screens here
};

// Define parameters for the main app stack navigator
// This stack wraps your tab navigator and also contains screens that can be pushed on top of tabs
export type AppStackParamList = {
    // The initial route of this stack is typically your Tab Navigator
    MainTabs: NavigatorScreenParams<AppTabParamList>; // <-- This is where your Tab Navigator lives
    JobDetail: { jobId: string }; // <-- JobDetail is directly in this stack
    CompanyDetail: { companyId: string }; // Example: other stack screen
    Application: { jobId: string };
    MyApplications: undefined;
    EditProfile: undefined;
    // Chat screens
    ChatList: undefined;
    ChatDetail: { conversationId: string };
    // ... any other screens that are pushed directly onto this stack
};

// Define the root navigation param list for your entire app
// This typically includes your authentication flow and the main app flow
export type RootStackParamList = {
    Auth: undefined; // Or { screen: 'Login' | 'Register' } if you navigate into Auth stack
    App: NavigatorScreenParams<AppStackParamList>; // Your main application flow, containing the AppStackParamList
    // Add any other top-level routes, e.g., Onboarding: undefined;
};